# Matrix Homeserver 手动部署指南（技术新手版）

## 📋 目录

1. [项目概述](#项目概述)
2. [前置条件和兼容性](#前置条件和兼容性)
3. [RouterOS基础知识](#routeros基础知识)
4. [系统环境准备](#系统环境准备)
5. [RouterOS设备配置](#routeros设备配置)
6. [网络和防火墙配置](#网络和防火墙配置)
7. [SSL证书配置](#ssl证书配置)
8. [动态IP监控配置](#动态ip监控配置)
9. [服务部署和配置](#服务部署和配置)
10. [配置验证和测试](#配置验证和测试)
11. [故障排除指南](#故障排除指南)
12. [安全配置最佳实践](#安全配置最佳实践)
13. [日常维护](#日常维护)
14. [常见问题FAQ](#常见问题faq)
15. [配置检查清单](#配置检查清单)

---

## 📖 项目概述

### 什么是Matrix Homeserver？

Matrix是一个开源的实时通信协议，Matrix Homeserver是运行Matrix协议的服务器软件。本项目提供了一个完整的Matrix Homeserver部署方案，特别针对以下场景设计：

- **动态IP环境**：家庭宽带等动态IP网络环境
- **ISP端口限制**：运营商封禁标准443端口的情况
- **分离式架构**：通过VPS提供稳定的联邦发现服务

### 项目特色

✅ **分离式部署架构**：内部核心服务 + 外部指路牌服务  
✅ **智能证书管理**：基于acme.sh的自动化SSL证书管理  
✅ **动态IP监控**：RouterOS API集成的IP变更自动同步  
✅ **三层符号链接**：避免证书重复申请的优雅架构  
✅ **完全自动化**：一键部署，自动运维  

### 部署架构图

```
┌─────────────────┐    ┌─────────────────┐
│   外部VPS       │    │   内网服务器     │
│ (静态IP:443)    │    │ (动态IP:8448)   │
│                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Nginx       │ │    │ │ Nginx       │ │
│ │ .well-known │ │    │ │ (反向代理)  │ │
│ └─────────────┘ │    │ └─────────────┘ │
│                 │    │ ┌─────────────┐ │
│                 │    │ │ Synapse     │ │
│                 │    │ │ (Matrix核心)│ │
│                 │    │ └─────────────┘ │
│                 │    │ ┌─────────────┐ │
│                 │    │ │ PostgreSQL  │ │
│                 │    │ │ Redis       │ │
│                 │    │ │ Coturn      │ │
│                 │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘
        │                       │
        └───────────────────────┘
           Matrix联邦通信
```

---

## 📋 前置条件和兼容性

### 支持的RouterOS设备

本项目的动态IP监控功能基于MikroTik RouterOS API，需要满足以下设备要求：

#### 🔧 硬件兼容性
- **MikroTik路由器系列**：
  - hEX系列 (RB750Gr3, hEX S等)
  - Cloud Core Router系列 (CCR1009, CCR1016等)
  - RouterBOARD系列 (RB4011, RB5009等)
  - Chateau系列 (Chateau 5G, Chateau LTE等)
  - wAP系列 (wAP ac, wAP R等)

#### 📱 RouterOS版本要求
- **最低版本**：RouterOS 6.43 或更高版本
- **推荐版本**：RouterOS 7.x 最新稳定版
- **重要说明**：RouterOS 6.43+版本支持`plaintext_login=True`参数，这是API连接的必需配置

#### 🐍 Python环境要求
- **Python版本**：Python 3.9 或更高版本
- **包管理器**：pip3 已安装并可用
- **Debian 12特别注意**：需要处理"外部管理环境"限制
- **依赖库**：routeros-api 0.21.0（项目会自动安装）

> ⚠️ **Debian 12重要提醒**：Debian 12引入了PEP 668规范，不允许直接使用`pip3 install`安装Python包。项目提供了自动化解决方案，详见系统环境准备章节。

#### 🌐 网络环境要求
- **动态公网IP**：支持家庭宽带、企业网络等动态IP环境
- **端口转发支持**：路由器必须支持NAT端口转发功能
- **内网连接**：Matrix服务器与RouterOS设备在同一局域网内
- **管理权限**：需要RouterOS设备的管理员访问权限

### 网络拓扑要求

```
互联网 ←→ [ISP] ←→ [MikroTik RouterOS] ←→ [内网] ←→ [Matrix服务器]
                        ↑                              ↑
                   动态公网IP                    固定内网IP
                   API端口8728                 SSH/管理端口
```

### 域名和DNS要求

#### 🌍 域名配置
- **主域名**：用于.well-known文件服务 (如: example.com)
- **Matrix子域名**：用于Matrix服务 (如: matrix.example.com)
- **DNS管理权限**：需要能够修改DNS记录的权限

#### ☁️ 支持的DNS服务商
- **Cloudflare** (推荐)：提供API支持，免费SSL证书
- **阿里云DNS**：支持API操作
- **腾讯云DNS**：支持API操作
- **其他支持API的DNS服务商**

---

## 🎓 RouterOS基础知识

### 什么是RouterOS？

**RouterOS**是MikroTik公司开发的网络操作系统，运行在MikroTik硬件设备上。它提供了企业级的路由、防火墙、带宽管理、VPN等网络功能。

#### 🔑 核心特性
- **企业级功能**：支持BGP、OSPF、MPLS等高级路由协议
- **强大的防火墙**：基于netfilter的防火墙系统
- **API接口**：提供编程接口，支持自动化管理
- **Web管理界面**：WebFig图形化管理界面
- **命令行界面**：强大的CLI管理工具

### 核心概念解释

#### 🌐 WAN接口 (Wide Area Network Interface)
- **定义**：连接到互联网服务提供商(ISP)的网络接口
- **作用**：获取公网IP地址，实现内网与互联网的通信
- **常见名称**：WAN、ether1、pppoe-out1等
- **IP获取方式**：DHCP、PPPoE、静态IP等

#### 🔌 API服务 (Application Programming Interface)
- **定义**：允许外部程序通过编程方式控制RouterOS的服务
- **端口**：默认8728端口（可自定义）
- **协议**：基于TCP的二进制协议
- **用途**：自动化配置、监控、管理RouterOS设备

#### 👥 用户权限系统
RouterOS采用基于组的权限管理系统：

| 权限组 | 权限范围 | 适用场景 | 安全级别 |
|--------|----------|----------|----------|
| `full` | 完全访问权限 | 系统管理员 | 🔴 高风险 |
| `write` | 读写权限 | 配置管理 | 🟡 中风险 |
| `read` | 只读权限 | 监控查看 | 🟢 低风险 |
| `api` | API访问权限 | 程序调用 | 🟡 中风险 |

#### 🔒 安全策略
- **policy参数**：控制用户可以执行的操作类型
- **常用组合**：`api,read` (API只读)、`api,write` (API读写)
- **最佳实践**：为不同用途创建专门的用户组

### RouterOS管理方式

#### 1️⃣ WebFig (Web界面)
- **访问方式**：浏览器访问 `http://路由器IP`
- **优点**：图形化界面，操作直观
- **缺点**：功能相对有限
- **适用场景**：基础配置、初学者使用

#### 2️⃣ WinBox (Windows客户端)
- **访问方式**：下载WinBox客户端连接
- **优点**：功能完整，界面友好
- **缺点**：仅支持Windows系统
- **适用场景**：日常管理、高级配置

#### 3️⃣ SSH/Telnet (命令行)
- **访问方式**：SSH客户端连接到路由器
- **优点**：功能最完整，支持脚本化
- **缺点**：需要学习命令语法
- **适用场景**：自动化脚本、高级用户

#### 4️⃣ API (编程接口)
- **访问方式**：通过编程语言调用API
- **优点**：完全自动化，可集成到应用中
- **缺点**：需要编程知识
- **适用场景**：自动化监控、批量管理

### 为什么选择RouterOS API？

#### ✅ 技术优势
1. **准确性**：直接从路由器获取真实的WAN接口IP，避免NAT或代理影响
2. **实时性**：路由器IP变化时能立即获取最新地址
3. **可靠性**：不依赖外部IP查询服务，避免网络限制或服务不可用
4. **安全性**：内网通信，不暴露查询行为给外部服务

#### 🎯 适用场景
- **动态IP环境**：家庭宽带等动态IP网络环境
- **企业内网**：需要高精度IP监控的生产环境
- **受限网络**：外部IP查询服务不稳定或被限制的网络环境
- **自动化需求**：需要集成到自动化系统中的场景

---

## 🖥️ 系统环境准备

### 硬件要求

#### 内网服务器（核心服务）
- **CPU**: 4核心（推荐8核心）
- **内存**: 8GB（推荐16GB）
- **存储**: 100GB SSD（推荐200GB以上）
- **网络**: 动态公网IP，支持端口转发

#### 外部VPS（指路牌服务）
- **CPU**: 1核心
- **内存**: 1GB
- **存储**: 20GB SSD
- **网络**: 静态公网IP，标准80/443端口

### 软件要求

#### 操作系统
- **推荐**: Ubuntu 20.04 LTS 或更新版本
- **支持**: Debian 11/12, CentOS 8+
- **架构**: x86_64 (amd64)

#### 必需软件包
```bash
# 基础工具
curl wget git nano vim unzip

# 系统工具
htop iotop netstat ss lsof

# 网络工具
dnsutils net-tools iputils-ping

# 安全工具
ufw fail2ban

# 开发工具
build-essential python3 python3-pip
```

### 第一步：更新系统

```bash
# 更新软件包列表
sudo apt update

# 升级已安装的软件包
sudo apt upgrade -y

# 安装基础工具包
sudo apt install -y curl wget git nano vim unzip htop iotop \
    dnsutils net-tools iputils-ping ufw fail2ban \
    build-essential python3 python3-pip

# 重启系统（推荐）
sudo reboot
```

### 第二步：创建专用用户

为了安全起见，我们创建一个专门的用户来运行Matrix Homeserver服务，而不是使用root用户。

#### 🔐 创建matrix用户

```bash
# 创建matrix用户（-m创建家目录，-s指定shell）
sudo useradd -m -s /bin/bash matrix

# 验证用户创建成功
id matrix
# 预期输出：uid=1001(matrix) gid=1001(matrix) groups=1001(matrix)
```

#### 🔑 设置用户密码

> ⚠️ **安全重要提醒**：必须为matrix用户设置强密码，这是基本的安全要求！

```bash
# 设置matrix用户密码
sudo passwd matrix

# 系统会提示输入新密码，请按照以下要求设置：
```

**强密码要求**：
- **最少12个字符**（推荐16个字符以上）
- **包含大写字母**：A-Z
- **包含小写字母**：a-z
- **包含数字**：0-9
- **包含特殊字符**：!@#$%^&*()等
- **避免使用**：字典词汇、个人信息、简单模式

**密码示例**（请勿直接使用）：
```
Matrix2025!@Server#
MySecure$Matrix&Home2025
Strong#Matrix!Server@2025
```

**设置密码的交互过程**：
```bash
sudo passwd matrix
# 输出：
# New password: [输入密码，不会显示]
# Retype new password: [再次输入密码确认]
# passwd: password updated successfully
```

#### 👥 配置用户权限

```bash
# 添加matrix用户到sudo组（获得管理员权限）
sudo usermod -aG sudo matrix

# 添加到docker组（稍后安装Docker后使用）
sudo usermod -aG docker matrix

# 验证用户组配置
groups matrix
# 预期输出：matrix : matrix sudo docker
```

#### 🔒 配置SSH密钥认证（推荐）

为了提高安全性，建议配置SSH密钥认证：

```bash
# 切换到matrix用户
su - matrix

# 创建SSH密钥目录
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# 如果你有SSH公钥，可以添加到authorized_keys
# 例如：echo "your-public-key-here" >> ~/.ssh/authorized_keys
# chmod 600 ~/.ssh/authorized_keys

# 退回到原用户
exit
```

#### ✅ 验证用户配置

```bash
# 测试matrix用户登录和sudo权限
su - matrix

# 验证当前用户
whoami
# 应该输出：matrix

# 验证sudo权限
sudo whoami
# 应该输出：root（需要输入matrix用户密码）

# 验证家目录
pwd
# 应该输出：/home/<USER>

# 验证shell
echo $SHELL
# 应该输出：/bin/bash

# 退回到原用户继续后续步骤
exit
```

#### 🛡️ 安全建议

1. **定期更换密码**：建议每3-6个月更换一次密码
2. **启用SSH密钥认证**：比密码认证更安全
3. **禁用密码SSH登录**：配置密钥后可以禁用密码登录
4. **监控登录日志**：定期检查`/var/log/auth.log`
5. **使用强密码管理器**：推荐使用密码管理器生成和存储密码

#### 🔧 故障排除

**如果忘记matrix用户密码**：
```bash
# 使用root或有sudo权限的用户重置密码
sudo passwd matrix
```

**如果用户创建失败**：
```bash
# 检查用户是否已存在
id matrix

# 如果存在但配置不正确，可以删除重建
sudo userdel -r matrix  # 谨慎使用，会删除用户和家目录
```

**如果sudo权限不生效**：
```bash
# 重新添加到sudo组
sudo usermod -aG sudo matrix

# 或者检查sudo组配置
sudo visudo
# 确保有这一行：%sudo   ALL=(ALL:ALL) ALL
```

### 第三步：安装Docker和Docker Compose

```bash
# 安装Docker
curl -fsSL https://get.docker.com | sh

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 验证Docker安装
docker --version
docker compose version

# 测试Docker运行
sudo docker run hello-world
```

### 第四步：安装acme.sh

```bash
# 安装acme.sh
curl https://get.acme.sh | sh -s email=<EMAIL>

# 重新加载shell配置
source ~/.bashrc

# 验证安装
acme.sh --version

# 设置默认CA为Let's Encrypt
acme.sh --set-default-ca --server letsencrypt
```

### 第五步：创建项目目录结构

在安装RouterOS API客户端之前，我们需要先创建项目的基础目录结构：

```bash
# 创建项目根目录
sudo mkdir -p /opt/matrix

# 设置目录所有者为matrix用户
sudo chown matrix:matrix /opt/matrix

# 切换到matrix用户
su - matrix

# 进入项目目录
cd /opt/matrix

# 创建基础目录结构
mkdir -p {config,data,logs,scripts,tools,venv}

# 验证目录结构
ls -la /opt/matrix
# 预期输出：
# drwxr-xr-x  2 <USER> <GROUP> 4096 config
# drwxr-xr-x  2 <USER> <GROUP> 4096 data
# drwxr-xr-x  2 <USER> <GROUP> 4096 logs
# drwxr-xr-x  2 <USER> <GROUP> 4096 scripts
# drwxr-xr-x  2 <USER> <GROUP> 4096 tools
# drwxr-xr-x  2 <USER> <GROUP> 4096 venv
```

### 第六步：配置Python虚拟环境（推荐方案）

> 🎯 **推荐理由**：虚拟环境是处理Debian 12 Python包管理限制的最佳方案，既安全又不影响系统环境。

#### 步骤1：安装虚拟环境工具

```bash
# 更新包列表
sudo apt update

# 安装Python虚拟环境工具
sudo apt install -y python3-venv python3-pip

# 验证安装
python3 -m venv --help | head -5
# 应该显示venv的帮助信息
```

#### 步骤2：创建项目专用虚拟环境

```bash
# 确保在项目目录中
cd /opt/matrix

# 创建虚拟环境（在venv目录中）
python3 -m venv venv

# 验证虚拟环境创建成功
ls -la venv/
# 应该看到：bin/ include/ lib/ pyvenv.cfg

# 检查虚拟环境的Python解释器
ls -la venv/bin/python*
# 应该看到：python, python3 等链接文件
```

#### 步骤3：激活虚拟环境并安装依赖

```bash
# 激活虚拟环境
source venv/bin/activate

# 验证虚拟环境已激活（命令提示符应该显示(venv)前缀）
which python3
# 应该显示：/opt/matrix/venv/bin/python3

# 升级pip到最新版本
pip install --upgrade pip

# 安装RouterOS API库
pip install routeros-api==0.21.0

# 验证安装成功
python3 -c "
import routeros_api
print('✅ RouterOS API库安装成功')
print('库路径:', routeros_api.__file__)
print('可用模块:', [x for x in dir(routeros_api) if not x.startswith('_')])
"

# 预期输出：
# ✅ RouterOS API库安装成功
# 库路径: /opt/matrix/venv/lib/python3.11/site-packages/routeros_api/__init__.py
# 可用模块: ['RouterOsApiPool', 'api_structure', 'connect', 'query']
```

#### 步骤4：创建虚拟环境管理脚本

为了方便使用，创建虚拟环境管理脚本：

```bash
# 创建激活脚本
cat > /opt/matrix/activate_venv.sh << 'EOF'
#!/bin/bash
# Matrix项目虚拟环境激活脚本

VENV_PATH="/opt/matrix/venv"

if [[ ! -d "$VENV_PATH" ]]; then
    echo "❌ 虚拟环境不存在: $VENV_PATH"
    echo "请先运行: python3 -m venv $VENV_PATH"
    exit 1
fi

echo "🔄 激活Matrix项目虚拟环境..."
source "$VENV_PATH/bin/activate"

echo "✅ 虚拟环境已激活"
echo "Python路径: $(which python3)"
echo "当前工作目录: $(pwd)"

# 检查RouterOS API库
if python3 -c "import routeros_api" 2>/dev/null; then
    echo "✅ RouterOS API库可用"
else
    echo "⚠️  RouterOS API库不可用，请安装: pip install routeros-api==0.21.0"
fi

echo ""
echo "💡 使用提示："
echo "- 退出虚拟环境: deactivate"
echo "- 重新激活: source /opt/matrix/activate_venv.sh"
echo "- 运行脚本: python3 script.py"
EOF

# 设置执行权限
chmod +x /opt/matrix/activate_venv.sh

# 创建Python脚本模板
cat > /opt/matrix/tools/routeros_test.py << 'EOF'
#!/opt/matrix/venv/bin/python3
"""
RouterOS API测试脚本
使用虚拟环境中的Python解释器
"""

import sys
import os

# 添加项目路径到Python路径
sys.path.insert(0, '/opt/matrix')

try:
    import routeros_api
    print("✅ RouterOS API库导入成功")
    print(f"库路径: {routeros_api.__file__}")
    print(f"Python解释器: {sys.executable}")
    print(f"虚拟环境: {os.environ.get('VIRTUAL_ENV', '未激活')}")
except ImportError as e:
    print(f"❌ RouterOS API库导入失败: {e}")
    sys.exit(1)

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试RouterOS API基本功能...")

    # 这里只测试库的可用性，不进行实际连接
    try:
        # 测试创建连接池对象（不实际连接）
        pool_class = routeros_api.RouterOsApiPool
        print(f"✅ RouterOsApiPool类可用: {pool_class}")

        print("✅ 基本功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== RouterOS API环境测试 ===")
    success = test_basic_functionality()

    if success:
        print("\n🎉 虚拟环境配置成功！")
        print("\n📝 下一步操作：")
        print("1. 配置RouterOS设备API服务")
        print("2. 运行完整的连接测试")
        print("3. 继续Matrix Homeserver部署")
    else:
        print("\n❌ 环境配置有问题，请检查安装步骤")
        sys.exit(1)
EOF

# 设置执行权限
chmod +x /opt/matrix/tools/routeros_test.py
```

#### 步骤5：测试虚拟环境配置

```bash
# 方式1：在激活的虚拟环境中运行
source /opt/matrix/activate_venv.sh
python3 /opt/matrix/tools/routeros_test.py

# 方式2：直接使用虚拟环境的Python解释器
/opt/matrix/venv/bin/python3 /opt/matrix/tools/routeros_test.py

# 预期输出：
# === RouterOS API环境测试 ===
# ✅ RouterOS API库导入成功
# 库路径: /opt/matrix/venv/lib/python3.11/site-packages/routeros_api/__init__.py
# Python解释器: /opt/matrix/venv/bin/python3
# 虚拟环境: /opt/matrix/venv
#
# 🧪 测试RouterOS API基本功能...
# ✅ RouterOsApiPool类可用: <class 'routeros_api.RouterOsApiPool'>
# ✅ 基本功能测试通过
#
# 🎉 虚拟环境配置成功！
```

#### 步骤6：配置环境变量和别名（可选）

为了方便日常使用，可以配置一些环境变量和别名：

```bash
# 编辑matrix用户的.bashrc文件
nano ~/.bashrc

# 在文件末尾添加以下内容：
cat >> ~/.bashrc << 'EOF'

# Matrix Homeserver项目配置
export MATRIX_HOME="/opt/matrix"
export MATRIX_VENV="$MATRIX_HOME/venv"

# 便捷别名
alias matrix-env='source $MATRIX_HOME/activate_venv.sh'
alias matrix-cd='cd $MATRIX_HOME'
alias matrix-test='$MATRIX_VENV/bin/python3 $MATRIX_HOME/tools/routeros_test.py'

# 自动激活虚拟环境（可选，如果希望登录时自动激活）
# source $MATRIX_HOME/activate_venv.sh
EOF

# 重新加载配置
source ~/.bashrc

# 测试别名
matrix-cd && pwd  # 应该显示 /opt/matrix
matrix-test       # 运行测试脚本
```

### 第七步：备选安装方案

如果虚拟环境方案不适合你的环境，可以使用以下备选方案：

#### 备选方案1：使用项目自动安装脚本

```bash
# 如果项目代码已下载，可以使用自动安装脚本
cd /opt/matrix
# 注意：此时可能还没有下载项目代码，这个方案在后续章节中使用
# ./tools/install_routeros_deps.sh
```

#### 备选方案2：系统级强制安装（Debian 12）

```bash
# 仅在虚拟环境方案不可行时使用
pip3 install --break-system-packages routeros-api==0.21.0

# 验证安装
python3 -c "import routeros_api; print('✅ RouterOS API已安装，库路径:', routeros_api.__file__)"
```

#### 备选方案3：用户级安装

```bash
# 安装到用户目录
pip3 install --user routeros-api==0.21.0

# 确保用户级包路径在PATH中
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# 验证安装
python3 -c "import routeros_api; print('✅ RouterOS API已安装')"
```

### ✅ 本步骤完成确认

完成本步骤后，请确认以下项目：

- [ ] **项目目录已创建**：`/opt/matrix`目录存在且权限正确
- [ ] **虚拟环境已创建**：`/opt/matrix/venv`目录存在
- [ ] **RouterOS API库已安装**：在虚拟环境中可以导入routeros_api
- [ ] **测试脚本运行成功**：`matrix-test`命令执行无错误
- [ ] **环境变量已配置**：别名和环境变量可正常使用

> 🎯 **下一步**：现在你已经完成了Python环境的配置，可以继续进行RouterOS设备的配置。

---

## 🔧 RouterOS设备配置

### 配置前的准备工作

#### 📋 配置检查清单
在开始配置之前，请确认以下项目：

- [ ] **设备连接**：RouterOS设备已正确连接到网络
- [ ] **管理访问**：能够通过WebFig、WinBox或SSH访问RouterOS
- [ ] **管理员权限**：拥有RouterOS设备的管理员账户
- [ ] **网络连通**：Matrix服务器与RouterOS设备在同一局域网
- [ ] **版本确认**：RouterOS版本为6.43或更高版本

#### 🔍 设备信息收集
在配置前，请记录以下信息：

```bash
# RouterOS设备信息
RouterOS IP地址: _______________  (如: ***********)
管理员用户名: _______________    (通常为: admin)
管理员密码: _______________      (设备当前密码)
RouterOS版本: _______________    (通过/system resource查看)
WAN接口名称: _______________     (通常为: WAN 或 ether1)

# Matrix服务器信息
服务器IP地址: _______________    (如: ***********00)
服务器用户名: _______________    (如: matrix)
```

### 步骤1：连接到RouterOS设备

#### 方法一：通过WebFig界面（推荐新手）

1. **打开浏览器**，访问RouterOS管理界面：
   ```
   http://***********
   ```
   > 💡 **说明**：将IP地址替换为你的RouterOS实际IP地址

2. **登录管理界面**：
   - 用户名：`admin`（默认）
   - 密码：你设置的管理员密码

3. **验证连接成功**：
   - 成功登录后会看到RouterOS的WebFig管理界面
   - 左侧菜单显示各种配置选项

#### 方法二：通过SSH连接（高级用户）

```bash
# 连接到RouterOS设备
ssh admin@***********

# 输入密码后进入RouterOS命令行界面
# 看到 [admin@MikroTik] > 提示符表示连接成功
```

### 步骤2：启用API服务

#### 🌐 通过WebFig界面配置

1. **进入服务配置**：
   - 点击左侧菜单 **IP** → **Services**
   - 在服务列表中找到 **api** 服务

2. **配置API服务**：
   - 双击 **api** 服务进行编辑
   - 配置以下参数：
     - **Disabled**: ❌ 取消勾选（启用服务）
     - **Port**: `8728`（默认API端口）
     - **Available From**: `***********/24`（限制访问网段）

3. **保存配置**：
   - 点击 **OK** 保存设置
   - API服务状态应显示为启用

#### 💻 通过SSH命令行配置

```bash
# 启用API服务
/ip service enable api

# 设置API端口（可选，默认8728）
/ip service set api port=8728

# 限制API访问来源（推荐安全配置）
/ip service set api address=***********/24

# 查看API服务状态
/ip service print where name=api
```

**预期输出示例**：
```
Flags: X - disabled, I - invalid
 #   NAME                  PORT ADDRESS         CERTIFICATE
 0   api                   8728 ***********/24
```

### 步骤3：创建专用API用户

> ⚠️ **安全提醒**：为了安全起见，强烈建议创建专门的API用户，而不是使用admin账户进行API访问。

#### 🌐 通过WebFig界面创建用户

**步骤1：进入用户管理界面**
1. 在RouterOS WebFig界面中，点击左侧菜单 **System** → **Users**
2. 点击 **+** 按钮添加新用户

**步骤2：配置用户基本信息**
- **Name**: `matrix-api`
- **Group**: `read`（只读权限，更安全）
- **Disabled**: ❌ 取消勾选（确保用户启用）

**步骤3：设置强密码**

> 🔐 **重要**：密码设置是最关键的安全步骤！

在 **Password** 字段中输入强密码，要求：
- **长度**：至少16个字符（推荐20个字符以上）
- **复杂度**：包含大小写字母、数字、特殊字符
- **唯一性**：不要重复使用其他系统的密码

**密码生成建议**：
```
# 可以在本地生成强密码，然后复制到WebFig界面
openssl rand -base64 24

# 或者手动创建，例如：
Matrix2025!API@RouterOS#Secure
RouterOS$API&Matrix!2025@Home
```

**步骤4：保存用户配置**
1. 确认所有信息填写正确
2. 点击 **OK** 保存用户配置
3. 在用户列表中确认新用户已创建

**步骤5：记录用户信息**

> 📝 **重要提醒**：请将用户信息安全地记录下来，后续配置需要使用！

```
RouterOS API用户信息：
- 用户名：matrix-api
- 密码：[您设置的强密码]
- 权限组：read
- 创建时间：[当前日期]
```

#### 💻 通过SSH命令行创建用户

> 🔑 **密码安全提醒**：RouterOS API用户密码同样需要设置强密码，这关系到整个网络的安全！

**步骤1：生成强密码**

```bash
# 方法1：使用openssl生成随机密码（推荐）
openssl rand -base64 24
# 示例输出：K8mN2pQ7vX9wR5tY3uI6oE1sA4dF

# 方法2：使用pwgen工具生成密码
# sudo apt install pwgen
# pwgen -s 20 1

# 方法3：手动创建强密码
# 示例：Matrix2025!API@RouterOS#
```

**RouterOS API密码要求**：
- **最少16个字符**（推荐20个字符以上）
- **包含大小写字母、数字和特殊字符**
- **避免使用**：admin、password、123456等弱密码
- **避免包含**：用户名、设备名称等可预测信息

**步骤2：创建API用户**

```bash
# 创建API专用用户（只读权限）
# 将下面的"YOUR_GENERATED_STRONG_PASSWORD"替换为实际生成的强密码
/user add name=matrix-api group=read password=YOUR_GENERATED_STRONG_PASSWORD

# 示例（请勿直接使用此密码）：
# /user add name=matrix-api group=read password=K8mN2pQ7vX9wR5tY3uI6oE1sA4dF

# 启用用户（确保未被禁用）
/user set matrix-api disabled=no

# 查看用户列表确认创建成功
/user print
```

**预期输出示例**：
```
Flags: X - disabled
 #   NAME                 GROUP                 ADDRESS
 0   admin                full
 1   matrix-api           read
```

**预期输出示例**：
```
Flags: X - disabled
 #   NAME                 GROUP                 ADDRESS
 0   admin                full
 1   matrix-api           read
```

#### 🔐 创建自定义权限组（高级配置）

如果需要更精细的权限控制，可以创建自定义权限组：

```bash
# 创建API只读权限组
/user group add name=api-readonly policy=api,read

# 创建API用户并分配到自定义组
/user add name=matrix-api group=api-readonly password=your_strong_password_here

# 查看权限组配置
/user group print where name=api-readonly
```

### 步骤4：配置安全访问控制

#### 🛡️ 限制API访问来源

```bash
# 仅允许Matrix服务器访问API（推荐）
/ip service set api address=***********00

# 或允许整个内网段访问
/ip service set api address=***********/24

# 查看当前配置
/ip service print where name=api
```

#### 🔥 配置防火墙规则（可选）

```bash
# 添加防火墙规则允许Matrix服务器访问API
/ip firewall filter add chain=input protocol=tcp dst-port=8728 \
    src-address=***********00 action=accept comment="Matrix API Access"

# 阻止其他来源访问API端口
/ip firewall filter add chain=input protocol=tcp dst-port=8728 \
    action=drop comment="Block unauthorized API access"

# 查看防火墙规则
/ip firewall filter print where dst-port=8728
```

### 步骤5：测试API连接

#### 🧪 基础连通性测试

```bash
# 从Matrix服务器测试网络连通性
ping ***********

# 测试API端口连通性
telnet *********** 8728
# 或使用nc命令
nc -zv *********** 8728
```

#### 🐍 Python API连接测试

在Matrix服务器上创建测试脚本：

```bash
# 创建测试脚本
cat > /tmp/test_routeros_api.py << 'EOF'
#!/usr/bin/env python3
"""RouterOS API连接测试脚本"""

import sys
try:
    import routeros_api
except ImportError:
    print("❌ 错误：未安装routeros_api库")
    print("请运行：pip3 install routeros-api")
    sys.exit(1)

# 配置参数（请根据实际情况修改）
ROUTEROS_HOST = "***********"
ROUTEROS_PORT = 8728
ROUTEROS_USER = "matrix-api"
ROUTEROS_PASSWORD = "your_password_here"

def test_connection():
    """测试RouterOS API连接"""
    try:
        print(f"🔗 正在连接到RouterOS: {ROUTEROS_HOST}:{ROUTEROS_PORT}")

        # 创建API连接
        connection = routeros_api.RouterOsApiPool(
            ROUTEROS_HOST,
            username=ROUTEROS_USER,
            password=ROUTEROS_PASSWORD,
            port=ROUTEROS_PORT,
            plaintext_login=True  # RouterOS 6.43+必需
        )

        api = connection.get_api()

        # 测试基本连接
        print("✅ API连接成功")

        # 获取系统信息
        identity = api.get_resource('/system/identity').get()
        if identity:
            print(f"✅ 设备名称: {identity[0].get('name', '未知')}")

        # 获取系统资源信息
        resource = api.get_resource('/system/resource').get()
        if resource:
            res = resource[0]
            print(f"✅ RouterOS版本: {res.get('version', '未知')}")
            print(f"✅ 设备架构: {res.get('architecture-name', '未知')}")

        # 测试获取接口信息
        interfaces = api.get_resource('/interface').get()
        print(f"✅ 发现 {len(interfaces)} 个网络接口")

        # 关闭连接
        connection.disconnect()
        print("✅ 测试完成，RouterOS API配置正确！")
        return True

    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("\n🔧 故障排除建议：")
        print("1. 检查RouterOS设备IP地址是否正确")
        print("2. 确认API服务已启用")
        print("3. 验证用户名和密码")
        print("4. 检查网络连通性")
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
EOF

# 设置执行权限
chmod +x /tmp/test_routeros_api.py

# 运行测试（请先修改脚本中的连接参数）
python3 /tmp/test_routeros_api.py
```

#### ✅ 预期测试结果

成功的测试输出应该类似：
```
🔗 正在连接到RouterOS: ***********:8728
✅ API连接成功
✅ 设备名称: MikroTik
✅ RouterOS版本: 7.11.2
✅ 设备架构: x86
✅ 发现 5 个网络接口
✅ 测试完成，RouterOS API配置正确！
```

### 配置完成确认

完成RouterOS配置后，请确认以下项目：

- [ ] **API服务已启用**：在RouterOS中API服务状态为启用
- [ ] **专用用户已创建**：matrix-api用户已创建并分配适当权限
- [ ] **访问控制已配置**：限制了API访问来源IP地址
- [ ] **连接测试成功**：Python API测试脚本运行成功
- [ ] **WAN接口已识别**：能够正确获取WAN接口信息

> 🎉 **恭喜！** RouterOS设备配置完成。接下来可以继续进行网络和防火墙配置。

---

## 🌐 网络和防火墙配置

### 端口规划

#### 内网服务器端口
- **8448**: HTTPS服务（Nginx反向代理，Matrix官方标准端口）
- **3478**: STUN服务（Coturn）
- **5349**: TURNS服务（Coturn）
- **49152-65535**: TURN UDP端口范围（Coturn）
- **8008**: Synapse内部端口（仅内网访问）
- **5432**: PostgreSQL（仅内网访问）
- **6379**: Redis（仅内网访问）

#### 8448端口选择说明

本项目使用**8448端口**作为Matrix HTTPS服务端口，这是Matrix官方标准的联邦通信端口，具有以下优势：

✅ **官方标准**：8448端口是Matrix联邦协议的官方标准端口，已在IANA注册为`matrix-fed`服务
✅ **更好兼容性**：完全符合Matrix规范，确保与所有Matrix实现的最佳兼容性
✅ **标准化部署**：遵循Matrix官方最佳实践，便于维护和升级
✅ **联邦发现**：通过.well-known文件重定向，其他Matrix服务器能正确发现和连接

**注意**：虽然某些ISP可能限制非标准端口，但通过外部VPS的.well-known重定向机制，可以完美解决这一问题。如果你的网络环境确实无法使用8448端口，可以考虑使用8443等备用端口。

#### 从8443端口迁移到8448端口

如果你之前使用的是8443端口，可以按以下步骤迁移到官方标准的8448端口：

1. **测试8448端口可用性**：
   ```bash
   # 测试端口是否被占用
   sudo netstat -tlnp | grep 8448

   # 测试路由器端口转发
   telnet your-external-ip 8448
   ```

2. **更新配置文件**：
   ```bash
   # 修改deployment.env
   sed -i 's/HTTPS_PORT="8443"/HTTPS_PORT="8448"/' config/deployment.env
   ```

3. **更新路由器端口转发**：
   - 将8443端口转发规则改为8448端口
   - 确保防火墙允许8448端口

4. **重新部署服务**：
   ```bash
   # 停止服务
   docker compose down

   # 重新生成配置
   ./scripts/setup.sh --skip-deps --skip-certs

   # 启动服务
   docker compose up -d
   ```

5. **验证迁移结果**：
   ```bash
   # 测试Matrix API
   curl -k https://matrix.example.com:8448/_matrix/client/versions
   ```

#### 外部VPS端口
- **80**: HTTP服务（用于证书申请和重定向）
- **443**: HTTPS服务（.well-known文件服务）

### 防火墙配置

#### 内网服务器防火墙设置

```bash
# 重置防火墙规则
sudo ufw --force reset

# 设置默认策略
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH（请根据实际SSH端口修改）
sudo ufw allow 22/tcp

# 允许Matrix服务端口（使用官方标准端口）
sudo ufw allow 8448/tcp comment 'Matrix HTTPS'
sudo ufw allow 3478/tcp comment 'STUN TCP'
sudo ufw allow 3478/udp comment 'STUN UDP'
sudo ufw allow 5349/tcp comment 'TURNS'
sudo ufw allow 49152:65535/udp comment 'TURN UDP Range'

# 启用防火墙
sudo ufw enable

# 查看防火墙状态
sudo ufw status verbose
```

#### 外部VPS防火墙设置

```bash
# 重置防火墙规则
sudo ufw --force reset

# 设置默认策略
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH
sudo ufw allow 22/tcp

# 允许HTTP和HTTPS
sudo ufw allow 80/tcp comment 'HTTP'
sudo ufw allow 443/tcp comment 'HTTPS'

# 启用防火墙
sudo ufw enable

# 查看防火墙状态
sudo ufw status verbose
```

### 路由器端口转发配置

你需要在路由器上配置端口转发，将以下端口转发到内网服务器：

| 外部端口 | 内部端口 | 协议 | 说明 |
|---------|---------|------|------|
| 8448 | 8448 | TCP | Matrix HTTPS服务（官方标准端口） |
| 3478 | 3478 | TCP/UDP | STUN服务 |
| 5349 | 5349 | TCP | TURNS服务 |
| 49152-65535 | 49152-65535 | UDP | TURN UDP端口范围 |

#### 常见路由器配置方法

**TP-Link路由器**：
1. 登录路由器管理界面
2. 进入"高级设置" → "NAT转发" → "虚拟服务器"
3. 添加上述端口转发规则

**华硕路由器**：
1. 登录路由器管理界面
2. 进入"外部网络" → "端口转发"
3. 添加端口转发规则

**MikroTik RouterOS**：
```bash
# 通过Winbox或命令行配置
/ip firewall nat
add action=dst-nat chain=dstnat dst-port=8448 protocol=tcp to-addresses=***********00 to-ports=8448
add action=dst-nat chain=dstnat dst-port=3478 protocol=tcp to-addresses=***********00 to-ports=3478
add action=dst-nat chain=dstnat dst-port=3478 protocol=udp to-addresses=***********00 to-ports=3478
add action=dst-nat chain=dstnat dst-port=5349 protocol=tcp to-addresses=***********00 to-ports=5349
add action=dst-nat chain=dstnat dst-port=49152-65535 protocol=udp to-addresses=***********00 to-ports=49152-65535
```

---

## 🔐 SSL证书配置

### 证书管理架构说明

本项目采用三层符号链接架构来管理SSL证书，这种设计有以下优势：

1. **避免重复申请**：使用acme.sh默认存储位置，避免Let's Encrypt速率限制
2. **支持多种证书**：自动检测并使用ECC或RSA证书
3. **原子化更新**：证书更新过程中服务不中断
4. **故障恢复**：符号链接损坏时自动修复

### 三层架构详解

```
源存储层（acme.sh默认位置）
/root/.acme.sh/matrix.example.com_ecc/    # ECC证书（优先）
/root/.acme.sh/matrix.example.com/        # RSA证书（备选）
    ↓ 符号链接
中间层（部署目录）
/opt/matrix/data/acme/matrix.example.com/
    ↓ 符号链接
服务层（各服务证书目录）
/opt/matrix/data/nginx/certs/ 和 /opt/matrix/data/coturn/certs/
```

### 配置Cloudflare DNS API

在申请SSL证书之前，需要配置Cloudflare DNS API：

```bash
# 获取Cloudflare API Token
# 1. 登录 https://dash.cloudflare.com/
# 2. 进入 "我的个人资料" → "API令牌"
# 3. 创建令牌，选择"自定义令牌"
# 4. 权限设置：
#    - 区域:区域:读取
#    - 区域:DNS:编辑
# 5. 区域资源：包含特定区域 - 选择你的域名

# 获取Zone ID
# 在Cloudflare域名管理页面右侧可以找到Zone ID
```

### 手动申请SSL证书

```bash
# 设置Cloudflare API环境变量
export CF_Token="your_cloudflare_api_token"
export CF_Zone_ID="your_zone_id"

# 申请ECC证书（推荐）
acme.sh --issue --dns dns_cf -d matrix.example.com --keylength ec-256

# 或申请RSA证书
acme.sh --issue --dns dns_cf -d matrix.example.com

# 查看已申请的证书
acme.sh --list

# 查看证书详细信息
acme.sh --info -d matrix.example.com

### 创建证书符号链接

证书申请成功后，需要创建符号链接供服务使用：

```bash
# 创建证书相关目录（如果不存在）
sudo mkdir -p /opt/matrix/data/acme/matrix.example.com
sudo mkdir -p /opt/matrix/data/nginx/certs
sudo mkdir -p /opt/matrix/data/coturn/certs

# 设置目录权限
sudo chown -R matrix:matrix /opt/matrix/data/

# 确定证书源目录（优先使用ECC证书）
DOMAIN="matrix.example.com"
if [ -d "/root/.acme.sh/${DOMAIN}_ecc" ]; then
    CERT_SOURCE="/root/.acme.sh/${DOMAIN}_ecc"
    echo "使用ECC证书: $CERT_SOURCE"
elif [ -d "/root/.acme.sh/${DOMAIN}" ]; then
    CERT_SOURCE="/root/.acme.sh/${DOMAIN}"
    echo "使用RSA证书: $CERT_SOURCE"
else
    echo "错误：未找到证书目录"
    exit 1
fi

# 创建中间层符号链接
ln -sf "${CERT_SOURCE}/fullchain.cer" "/opt/matrix/data/acme/${DOMAIN}/fullchain.cer"
ln -sf "${CERT_SOURCE}/${DOMAIN}.key" "/opt/matrix/data/acme/${DOMAIN}/private.key"
ln -sf "${CERT_SOURCE}/ca.cer" "/opt/matrix/data/acme/${DOMAIN}/ca.cer"

# 创建服务层符号链接
# Nginx证书链接
ln -sf "/opt/matrix/data/acme/${DOMAIN}/fullchain.cer" "/opt/matrix/data/nginx/certs/fullchain.cer"
ln -sf "/opt/matrix/data/acme/${DOMAIN}/private.key" "/opt/matrix/data/nginx/certs/private.key"

# Coturn证书链接
ln -sf "/opt/matrix/data/acme/${DOMAIN}/fullchain.cer" "/opt/matrix/data/coturn/certs/fullchain.cer"
ln -sf "/opt/matrix/data/acme/${DOMAIN}/private.key" "/opt/matrix/data/coturn/certs/private.key"

# 验证符号链接
echo "验证证书符号链接："
ls -la /opt/matrix/data/nginx/certs/
ls -la /opt/matrix/data/coturn/certs/
```

---

## 📡 动态IP监控配置

### 动态IP监控原理

Matrix Homeserver项目采用智能的动态IP监控机制，确保在IP地址变更时能够自动更新DNS记录和服务配置。

#### 🔄 监控流程
```
1. 获取当前公网IP → 2. 与上次记录对比 → 3. 检测到变化 → 4. 更新DNS记录 → 5. 更新Coturn配置 → 6. 重启相关服务
```

#### 📊 IP获取优先级
1. **RouterOS API**（推荐）：直接从路由器获取WAN接口IP
2. **外部IP服务**（备选）：通过公网服务查询IP地址

### Matrix项目集成配置

> 📝 **前提条件**：确保已完成前面章节的RouterOS设备配置

#### 步骤1：编辑项目配置文件

在主配置文件中添加RouterOS相关配置：

```bash
# 编辑部署配置文件
nano /opt/matrix/config/deployment.env

# 添加或修改以下RouterOS配置项
```

**RouterOS API配置部分**：
```bash
# ================================================================
# RouterOS API配置（动态IP监控）
# ================================================================

# RouterOS设备连接信息
ROUTEROS_HOST="***********"                    # RouterOS设备IP地址
ROUTEROS_PORT="8728"                           # API端口（默认8728）
ROUTEROS_USER="matrix-api"                     # API用户名
ROUTEROS_PASSWORD="your_secure_password"       # API用户密码
ROUTEROS_WAN_INTERFACE="WAN"                   # WAN接口名称（可自定义）
ROUTEROS_TIMEOUT="10"                          # 连接超时时间（秒）

# 启用RouterOS API监控（true/false）
ENABLE_ROUTEROS_MONITORING="true"

# IP监控配置
IP_CHECK_INTERVAL="60"                         # IP检查间隔（秒）
IP_UPDATE_MAX_RETRIES="3"                      # 最大重试次数
IP_UPDATE_RETRY_DELAY="10"                     # 重试延迟（秒）
```

**Cloudflare DNS配置部分**：
```bash
# ================================================================
# Cloudflare DNS配置
# ================================================================

# Cloudflare API配置
CLOUDFLARE_API_TOKEN="your_cloudflare_api_token"   # Cloudflare API令牌
CLOUDFLARE_ZONE_ID="your_cloudflare_zone_id"       # DNS区域ID
CLOUDFLARE_EMAIL="<EMAIL>"          # Cloudflare账户邮箱

# DNS记录配置
DNS_RECORD_NAME="matrix.example.com"               # Matrix服务域名
DNS_RECORD_TTL="300"                               # DNS记录TTL（秒）
```

#### 步骤2：获取Cloudflare API凭据

> 🔑 **重要**：需要获取Cloudflare API令牌和区域ID来自动更新DNS记录

##### 获取API令牌

1. **登录Cloudflare控制台**：
   - 访问 https://dash.cloudflare.com/
   - 使用你的Cloudflare账户登录

2. **创建API令牌**：
   - 点击右上角头像 → **My Profile**
   - 选择 **API Tokens** 标签页
   - 点击 **Create Token**

3. **配置令牌权限**：
   - 模板选择：**Custom token**
   - 权限设置：
     - `Zone:Zone:Read`
     - `Zone:DNS:Edit`
   - 区域资源：选择你的域名
   - 点击 **Continue to summary** → **Create Token**

4. **保存令牌**：
   - 复制生成的API令牌（只显示一次）
   - 将令牌填入配置文件的`CLOUDFLARE_API_TOKEN`

##### 获取区域ID

1. **进入域名管理**：
   - 在Cloudflare控制台中选择你的域名
   - 在右侧边栏找到 **Zone ID**
   - 复制Zone ID并填入配置文件的`CLOUDFLARE_ZONE_ID`

#### 步骤3：配置WAN接口名称

> 💡 **说明**：不同的RouterOS配置可能使用不同的WAN接口名称

##### 查看可用接口

```bash
# 在RouterOS中查看所有接口
/interface print

# 查看接口IP地址分配
/ip address print
```

**常见WAN接口名称**：
- `WAN` - 默认WAN接口名称
- `ether1` - 第一个以太网接口
- `pppoe-out1` - PPPoE拨号接口
- `lte1` - LTE移动网络接口

##### 自动检测逻辑

项目的IP监控脚本会按以下优先级自动检测WAN接口：

1. **用户指定接口**：`ROUTEROS_WAN_INTERFACE`配置的接口名
2. **默认WAN接口**：名为"WAN"的接口
3. **关键字匹配**：包含"wan"关键字的接口
4. **第一个以太网接口**：ether1
5. **PPPoE接口**：pppoe-out1
6. **第一个启用接口**：状态为启用的接口

#### 步骤4：验证RouterOS API集成

> 🧪 **重要**：在继续部署之前，必须验证RouterOS API配置是否正确

##### 使用项目内置验证工具

> 📝 **注意**：以下工具需要在下载项目代码后才能使用。如果还没有下载项目代码，请先使用手动验证步骤。

```bash
# 确保在项目目录中（如果已下载项目代码）
cd /opt/matrix

# 方式1：使用虚拟环境（推荐）
source venv/bin/activate

# 使用RouterOS客户端测试连接
python3 tools/routeros_client.py --host *********** --user matrix-api --password your_password test

# 获取WAN接口IP
python3 tools/routeros_client.py --host *********** --user matrix-api --password your_password get-wan-ip

# 验证配置文件（如果配置文件已存在）
python3 tools/validate_config.py --config-file internal/config/deployment.env

# 方式2：直接使用虚拟环境的Python解释器
/opt/matrix/venv/bin/python3 tools/routeros_client.py --host *********** --user matrix-api --password your_password test
```

##### 手动验证步骤

**1. 验证网络连通性**：
```bash
# 测试到RouterOS设备的网络连通性
ping -c 3 ***********

# 测试API端口连通性
nc -zv *********** 8728
# 或使用telnet
telnet *********** 8728
```

**2. 验证API服务状态**：
```bash
# 在RouterOS设备上检查API服务状态
/ip service print where name=api

# 预期输出应显示API服务已启用
# Flags: X - disabled, I - invalid
#  #   NAME                  PORT ADDRESS
#  0   api                   8728 ***********/24
```

**3. 验证用户权限**：
```bash
# 检查API用户是否存在
/user print where name=matrix-api

# 检查用户组权限
/user group print where name=read
```

**4. 测试WAN接口检测**：
```bash
# 查看所有接口
/interface print

# 查看IP地址分配
/ip address print

# 确认WAN接口名称和IP地址
```

##### 预期验证结果

成功的验证应该显示：
```
✅ 网络连通性测试通过
✅ API端口8728可访问
✅ RouterOS API连接成功
✅ 用户认证成功
✅ WAN接口检测成功
✅ 获取到公网IP: xxx.xxx.xxx.xxx
✅ 所有验证项目通过
```

#### 步骤5：配置IP监控服务

##### 启用IP监控功能

```bash
# 编辑部署配置文件
nano /opt/matrix/config/deployment.env

# 确保以下配置项正确设置
ENABLE_ROUTEROS_MONITORING="true"
ENABLE_IP_WATCHDOG="true"
ENABLE_DNS_UPDATE="true"
```

##### 配置监控参数

根据你的网络环境调整监控参数：

```bash
# 快速响应配置（推荐家庭网络）
IP_CHECK_INTERVAL="60"          # 每分钟检查一次
IP_UPDATE_MAX_RETRIES="3"       # 最大重试3次
IP_UPDATE_RETRY_DELAY="10"      # 重试间隔10秒

# 保守配置（企业网络或稳定环境）
IP_CHECK_INTERVAL="300"         # 每5分钟检查一次
IP_UPDATE_MAX_RETRIES="5"       # 最大重试5次
IP_UPDATE_RETRY_DELAY="30"      # 重试间隔30秒
```

#### 步骤6：测试完整的IP监控流程

##### 手动触发IP检查

```bash
# 运行IP监控脚本进行测试
cd /opt/matrix
./internal/scripts/ip_watchdog.sh --test-mode

# 查看详细日志
tail -f /var/log/matrix/ip_watchdog.log
```

##### 模拟IP变更测试

```bash
# 临时修改DNS记录来模拟IP变更
# 注意：这会暂时影响服务访问

# 1. 记录当前IP
current_ip=$(dig +short matrix.example.com)
echo "当前IP: $current_ip"

# 2. 手动修改DNS记录为测试IP
# （通过Cloudflare控制台或API）

# 3. 运行IP监控脚本
./internal/scripts/ip_watchdog.sh --force-update

# 4. 验证DNS记录是否恢复
new_ip=$(dig +short matrix.example.com)
echo "更新后IP: $new_ip"
```

### 配置定时任务

```bash
# 创建日志目录
sudo mkdir -p /var/log/matrix
sudo chown matrix:matrix /var/log/matrix

# 编辑crontab
crontab -e

# 添加以下定时任务
# IP监控 - 每分钟执行
* * * * * /opt/matrix/scripts/ip_watchdog.sh >> /var/log/matrix/ip_watchdog.log 2>&1

# 证书管理 - 每天凌晨2点执行
0 2 * * * /opt/matrix/scripts/certificate_manager.sh >> /var/log/matrix/certificate_manager.log 2>&1

# 健康检查 - 每5分钟执行
*/5 * * * * /opt/matrix/scripts/health_check.sh >> /var/log/matrix/health_check.log 2>&1

# 备份 - 每天凌晨3点执行
0 3 * * * /opt/matrix/scripts/backup.sh >> /var/log/matrix/backup.log 2>&1
```

---

## 🚀 服务部署和配置

### 下载项目代码

```bash
# 切换到matrix用户
su - matrix

# 进入已创建的项目目录
cd /opt/matrix

# 下载项目代码（请替换为实际的仓库地址）
git clone <repository-url> .

# 或者如果没有git仓库，创建额外的目录结构
mkdir -p {config,scripts,data/{nginx,coturn,synapse,postgres,redis,acme,logs,backup}}

# 验证目录结构
ls -la /opt/matrix
```

### 配置环境变量

```bash
# 复制配置模板
cp config/deployment.env.template config/deployment.env

# 编辑配置文件
nano config/deployment.env
```

**重要配置项说明**：

```bash
# ================================================================
# 基础域名配置
# ================================================================
DOMAIN="example.com"                    # 你的主域名
SUBDOMAIN_MATRIX="matrix"               # Matrix服务子域名
HTTPS_PORT="8448"                       # HTTPS服务端口（Matrix官方标准端口）

# ================================================================
# 数据库配置
# ================================================================
DB_NAME="synapse"                       # 数据库名称
DB_USER="synapse"                       # 数据库用户
DB_PASSWORD="$(openssl rand -base64 32)" # 数据库密码（自动生成）

# ================================================================
# Cloudflare DNS配置
# ================================================================
CLOUDFLARE_API_TOKEN="your_token_here"  # Cloudflare API令牌
CLOUDFLARE_ZONE_ID="your_zone_id_here"  # Cloudflare Zone ID

# ================================================================
# TURN服务器配置
# ================================================================
COTURN_SHARED_SECRET="$(openssl rand -base64 32)"  # Coturn共享密钥
TURN_PORT="3478"                        # TURN端口
TURNS_PORT="5349"                       # TURNS端口
COTURN_MIN_PORT="49152"                 # TURN UDP端口范围开始
COTURN_MAX_PORT="65535"                 # TURN UDP端口范围结束

# ================================================================
# Synapse配置
# ================================================================
SYNAPSE_ADMIN_USER="admin"              # 管理员用户名
SYNAPSE_FORM_SECRET="$(openssl rand -base64 32)"     # 表单密钥
SYNAPSE_MACAROON_SECRET="$(openssl rand -base64 32)" # Macaroon密钥
```

### 生成配置文件

项目提供了自动化脚本来生成所有必需的配置文件：

```bash
# 运行初始化脚本
./scripts/setup.sh

# 如果需要强制重新初始化
./scripts/setup.sh --force

# 如果只想生成配置文件（跳过其他步骤）
./scripts/setup.sh --skip-deps --skip-certs

### 手动配置各个服务

如果你想了解每个服务的配置细节，可以手动配置：

#### 1. PostgreSQL数据库配置

```bash
# 创建PostgreSQL初始化脚本
cat > config/postgres-init.sql << 'EOF'
-- PostgreSQL初始化脚本
-- 为Matrix Synapse创建数据库和用户

-- 创建数据库（如果不存在）
CREATE DATABASE synapse
    ENCODING 'UTF8'
    LC_COLLATE 'C'
    LC_CTYPE 'C'
    TEMPLATE template0;

-- 创建用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'synapse') THEN
        CREATE USER synapse WITH PASSWORD 'your_db_password';
    END IF;
END
$$;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE synapse TO synapse;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO synapse;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO synapse;
EOF
```

#### 2. Nginx反向代理配置

```bash
# 创建Nginx主配置文件
cat > config/nginx.conf << 'EOF'
# Nginx主配置文件
# 针对Matrix Homeserver优化

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 工作进程配置
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# HTTP配置块
http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # 客户端配置
    client_max_body_size 50M;  # Matrix媒体文件上传限制
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
EOF

# 创建Matrix站点配置
cat > config/matrix.conf << 'EOF'
# Matrix Homeserver站点配置
# 处理Matrix客户端和联邦服务器的请求

server {
    listen 8448 ssl http2;
    server_name matrix.example.com;  # 替换为你的域名

    # SSL证书配置（使用符号链接）
    ssl_certificate /etc/nginx/certs/fullchain.cer;
    ssl_certificate_key /etc/nginx/certs/private.key;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头部
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Matrix客户端API代理
    location /_matrix {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;

        # 客户端IP传递
        proxy_set_header X-Real-IP $remote_addr;

        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 缓冲配置
        proxy_buffering off;
    }

    # 健康检查端点
    location /_matrix/client/versions {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;

        # 缓存配置
        add_header Cache-Control "public, max-age=3600";
    }

    # 媒体文件代理（大文件上传）
    location /_matrix/media {
        proxy_pass http://synapse:8008;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $host;

        # 大文件上传配置
        client_max_body_size 50M;
        proxy_request_buffering off;
    }

    # 根路径重定向到Element Web
    location = / {
        return 301 https://app.element.io/;
    }

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
EOF
```

#### 3. Synapse配置文件

```bash
# 创建Synapse主配置文件
cat > config/homeserver.yaml << 'EOF'
# Matrix Synapse Homeserver配置文件
# 详细配置说明：https://matrix-org.github.io/synapse/latest/usage/configuration/

# ================================================================
# 服务器基础配置
# ================================================================

# 服务器名称（必须与域名匹配）
server_name: "example.com"

# PID文件位置
pid_file: /data/homeserver.pid

# 公共访问URL（客户端连接地址）
public_baseurl: https://matrix.example.com:8448/

# Web客户端位置（可选，重定向到Element）
web_client_location: https://app.element.io/

# ================================================================
# 网络监听配置
# ================================================================

listeners:
  # HTTP监听器（内部通信）
  - port: 8008
    tls: false
    type: http
    x_forwarded: true  # 信任反向代理的X-Forwarded-* 头部
    bind_addresses: ['0.0.0.0']

    resources:
      # 客户端API和联邦API
      - names: [client, federation]
        compress: false  # 由Nginx处理压缩

# ================================================================
# 数据库配置
# ================================================================

database:
  name: psycopg2  # PostgreSQL驱动
  args:
    user: synapse
    password: your_db_password  # 替换为实际密码
    database: synapse
    host: db  # Docker容器名称
    port: 5432
    cp_min: 5      # 最小连接池大小
    cp_max: 10     # 最大连接池大小

# ================================================================
# Redis缓存配置
# ================================================================

redis:
  enabled: true
  host: redis    # Docker容器名称
  port: 6379
  # password: your_redis_password  # 如果Redis设置了密码

# ================================================================
# 媒体存储配置
# ================================================================

media_store_path: /data/media_store

# 媒体文件大小限制
max_upload_size: 50M

# 媒体文件保留策略
media_retention:
  # 本地媒体保留时间（天）
  local_media_lifetime: 365d
  # 远程媒体保留时间（天）
  remote_media_lifetime: 90d

# ================================================================
# 用户注册配置
# ================================================================

# 是否允许新用户注册
enable_registration: false

# 注册共享密钥（如果启用注册）
# registration_shared_secret: "your_registration_secret"

# ================================================================
# 联邦配置
# ================================================================

# 联邦域名白名单（空列表表示允许所有域名）
federation_domain_whitelist: []

# 联邦IP黑名单（阻止私有网络）
federation_ip_range_blacklist:
  - '*********/8'    # 本地回环
  - '10.0.0.0/8'     # 私有网络A类
  - '**********/12'  # 私有网络B类
  - '***********/16' # 私有网络C类
  - '**********/10'  # 运营商级NAT
  - '***********/16' # 链路本地
  - '::1/128'        # IPv6本地回环
  - 'fe80::/64'      # IPv6链路本地
  - 'fc00::/7'       # IPv6私有网络

# ================================================================
# TURN服务器配置（用于音视频通话）
# ================================================================

turn_uris:
  - "turn:matrix.example.com:3478?transport=udp"
  - "turn:matrix.example.com:3478?transport=tcp"
  - "turns:matrix.example.com:5349?transport=tcp"

turn_shared_secret: "your_coturn_shared_secret"  # 与Coturn配置保持一致
turn_user_lifetime: 1h      # TURN用户生存时间
turn_allow_guests: true     # 允许访客使用TURN

# ================================================================
# 安全配置
# ================================================================

# 密码哈希轮数
bcrypt_rounds: 12

# 表单密钥（用于CSRF保护）
form_secret: "your_form_secret"

# Macaroon密钥（用于访问令牌）
macaroon_secret_key: "your_macaroon_secret"

# ================================================================
# 速率限制配置
# ================================================================

# 消息发送速率限制
rc_message:
  per_second: 0.2    # 每秒最多0.2条消息
  burst_count: 10    # 突发最多10条消息

# 用户注册速率限制
rc_registration:
  per_second: 0.17   # 每秒最多0.17次注册
  burst_count: 3     # 突发最多3次注册

# 登录速率限制
rc_login:
  address:
    per_second: 0.17
    burst_count: 3
  account:
    per_second: 0.17
    burst_count: 3
  failed_attempts:
    per_second: 0.17
    burst_count: 3

# ================================================================
# 日志配置
# ================================================================

log_config: "/data/log.config"

# ================================================================
# 签名密钥配置
# ================================================================

signing_key_path: "/data/signing.key"

# 信任的密钥服务器
trusted_key_servers:
  - server_name: "matrix.org"

# 抑制密钥服务器警告
suppress_key_server_warning: true

# ================================================================
# 实验性功能
# ================================================================

experimental_features:
  spaces_enabled: true      # 启用Spaces功能
  msc3026_enabled: true     # 启用忙碌状态

# ================================================================
# 工作进程配置（单进程模式）
# ================================================================

worker_app: synapse.app.homeserver
worker_log_config: /data/log.config
EOF

# 创建Synapse日志配置
cat > config/log.config << 'EOF'
# Synapse日志配置文件

version: 1

formatters:
  precise:
    format: '%(asctime)s - %(name)s - %(lineno)d - %(levelname)s - %(request)s - %(message)s'

handlers:
  file:
    class: logging.handlers.TimedRotatingFileHandler
    formatter: precise
    filename: /data/logs/homeserver.log
    when: midnight
    backupCount: 7  # 保留7天的日志
    encoding: utf8

  console:
    class: logging.StreamHandler
    formatter: precise

loggers:
  synapse.storage.SQL:
    # 数据库查询日志级别
    level: INFO

  synapse.http.server:
    # HTTP服务器日志级别
    level: INFO

root:
  level: INFO
  handlers: [file, console]

disable_existing_loggers: false
EOF
```

#### 4. Coturn TURN服务器配置

```bash
# 创建Coturn配置文件
cat > config/turnserver.conf << 'EOF'
# Coturn TURN/STUN服务器配置文件
# 用于Matrix音视频通话的NAT穿透

# ================================================================
# 基础服务配置
# ================================================================

# 监听端口配置
listening-port=3478          # STUN/TURN端口
tls-listening-port=5349      # TURNS端口（TLS加密）

# 监听地址（0.0.0.0表示所有接口）
listening-ip=0.0.0.0

# 外部IP地址（动态获取，由脚本自动更新）
external-ip=YOUR_EXTERNAL_IP

# ================================================================
# 中继端口范围配置
# ================================================================

# UDP中继端口范围（需要在路由器上开放这些端口）
min-port=49152
max-port=65535

# ================================================================
# 认证配置
# ================================================================

# 使用长期凭证机制
use-auth-secret
static-auth-secret=your_coturn_shared_secret  # 与Synapse配置保持一致

# 认证域名
realm=matrix.example.com  # 替换为你的域名

# ================================================================
# SSL/TLS证书配置
# ================================================================

# 证书文件路径（使用符号链接）
cert=/etc/coturn/certs/fullchain.cer
pkey=/etc/coturn/certs/private.key

# ================================================================
# 安全配置
# ================================================================

# 禁用不安全的协议
no-sslv2
no-sslv3

# 启用指纹验证
fingerprint

# 禁用CLI（命令行接口）
no-cli

# ================================================================
# 日志配置
# ================================================================

# 日志文件路径
log-file=/var/log/coturn/turnserver.log

# 详细日志级别
verbose

# ================================================================
# 性能优化配置
# ================================================================

# 用户配额（每个用户最大会话数）
user-quota=12

# 总配额（服务器最大会话数）
total-quota=1200

# ================================================================
# 网络配置
# ================================================================

# 禁用本地回环地址
no-loopback-peers

# 禁用多播地址
no-multicast-peers

# 允许的对等地址范围
denied-peer-ip=0.0.0.0-*************
denied-peer-ip=10.0.0.0-1*************
denied-peer-ip=**********-***************
denied-peer-ip=*********-***************
denied-peer-ip=***********-***************
denied-peer-ip=**********-**************
denied-peer-ip=*********-***********
denied-peer-ip=*********-***********
denied-peer-ip=***********-*************
denied-peer-ip=***********-***************
denied-peer-ip=**********-**************
denied-peer-ip=************-**************
denied-peer-ip=***********-*************
denied-peer-ip=240.0.0.0-***************

# IPv6禁用范围
denied-peer-ip=::1
denied-peer-ip=64:ff9b::-64:ff9b::ffff:ffff
denied-peer-ip=::ffff:0.0.0.0-::ffff:***************
denied-peer-ip=2001::-2001:1ff:ffff:ffff:ffff:ffff:ffff:ffff
denied-peer-ip=2002::-2002:ffff:ffff:ffff:ffff:ffff:ffff:ffff
denied-peer-ip=fc00::-fdff:ffff:ffff:ffff:ffff:ffff:ffff:ffff
denied-peer-ip=fe80::-febf:ffff:ffff:ffff:ffff:ffff:ffff:ffff
EOF
```

#### 5. Docker Compose配置

```bash
# 创建Docker Compose配置文件
cat > docker-compose.yml << 'EOF'
# Matrix Homeserver Docker Compose配置
# 定义所有服务的容器编排

version: '3.8'

# 定义网络
networks:
  matrix_network:
    driver: bridge

# 定义服务
services:
  # PostgreSQL数据库服务
  db:
    image: postgres:15-alpine
    container_name: matrix_postgres
    restart: unless-stopped

    # 环境变量配置
    environment:
      POSTGRES_DB: ${DB_NAME:-synapse}
      POSTGRES_USER: ${DB_USER:-synapse}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"

    # 数据卷挂载
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./config/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql:ro

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${POSTGRES_MEMORY_LIMIT:-1g}
          cpus: '${POSTGRES_CPU_LIMIT:-1}'

    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-synapse}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: matrix_redis
    restart: unless-stopped

    # Redis启动命令（配置内存限制和淘汰策略）
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

    # 数据卷挂载
    volumes:
      - ./data/redis:/data

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${REDIS_MEMORY_LIMIT:-512m}
          cpus: '${REDIS_CPU_LIMIT:-0.5}'

    # 健康检查
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Synapse Matrix服务器
  synapse:
    image: matrixdotorg/synapse:latest
    container_name: matrix_synapse
    restart: unless-stopped

    # 服务依赖（等待数据库和Redis启动）
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

    # 环境变量配置
    environment:
      SYNAPSE_SERVER_NAME: ${DOMAIN}
      SYNAPSE_REPORT_STATS: "no"
      SYNAPSE_CONFIG_PATH: /data/homeserver.yaml

    # 数据卷挂载
    volumes:
      - ./data/synapse:/data
      - ./config/homeserver.yaml:/data/homeserver.yaml:ro
      - ./config/log.config:/data/log.config:ro

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${SYNAPSE_MEMORY_LIMIT:-2g}
          cpus: '${SYNAPSE_CPU_LIMIT:-2}'

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/_matrix/client/versions"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理服务
  nginx:
    image: nginx:alpine
    container_name: matrix_nginx
    restart: unless-stopped

    # 服务依赖（等待Synapse启动）
    depends_on:
      synapse:
        condition: service_healthy

    # 端口映射
    ports:
      - "${HTTPS_PORT:-8448}:8448"

    # 数据卷挂载
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/matrix.conf:/etc/nginx/conf.d/matrix.conf:ro
      - ./data/nginx/certs:/etc/nginx/certs:ro
      - ./data/nginx/logs:/var/log/nginx

    # 网络配置
    networks:
      - matrix_network

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${NGINX_MEMORY_LIMIT:-256m}
          cpus: '${NGINX_CPU_LIMIT:-0.5}'

    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8448/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Coturn TURN/STUN服务器
  coturn:
    image: coturn/coturn:latest
    container_name: matrix_coturn
    restart: unless-stopped

    # 使用主机网络模式（TURN服务需要直接访问网络）
    network_mode: host

    # 数据卷挂载
    volumes:
      - ./data/coturn/conf/turnserver.conf:/etc/coturn/turnserver.conf:ro
      - ./data/coturn/certs:/etc/coturn/certs:ro
      - ./data/coturn/logs:/var/log/coturn
      # 挂载acme.sh目录以访问原始证书（只读）
      - /root/.acme.sh:/root/.acme.sh:ro

    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${COTURN_MEMORY_LIMIT:-512m}
          cpus: '${COTURN_CPU_LIMIT:-1}'

    # 健康检查
    healthcheck:
      test: ["CMD", "turnutils_uclient", "-t", "127.0.0.1", "-p", "3478"]
      interval: 60s
      timeout: 10s
      retries: 3
EOF

### 启动服务

配置完成后，可以启动所有服务：

```bash
# 确保在项目根目录
cd /opt/matrix

# 加载环境变量
source config/deployment.env

# 创建必要的目录
mkdir -p data/{postgres,redis,synapse,nginx/logs,coturn/{conf,certs,logs},logs,backup}

# 设置目录权限
sudo chown -R matrix:matrix data/

# 拉取Docker镜像
docker compose pull

# 启动服务（后台运行）
docker compose up -d

# 查看服务状态
docker compose ps

# 查看服务日志
docker compose logs -f
```

### 创建管理员用户

服务启动后，需要创建Matrix管理员用户：

```bash
# 方法1：使用管理脚本
./scripts/admin.sh user create admin --admin

# 方法2：直接使用Docker命令
docker compose exec synapse register_new_matrix_user -c /data/homeserver.yaml http://localhost:8008

# 按提示输入用户名、密码，并选择是否为管理员
```

---

## 🔍 配置验证和测试

### 部署前验证检查清单

在开始正式部署之前，请确认以下所有配置项目都已正确完成：

#### 🖥️ 系统环境验证

- [ ] **操作系统**：Debian 12 或 Ubuntu 22.04 LTS
- [ ] **用户权限**：matrix用户已创建并配置sudo权限
- [ ] **Docker环境**：Docker和Docker Compose已安装并正常运行
- [ ] **网络连接**：服务器可以正常访问互联网
- [ ] **磁盘空间**：至少20GB可用空间

```bash
# 快速系统环境检查
echo "=== 系统环境检查 ==="
echo "操作系统: $(lsb_release -d | cut -f2)"
echo "当前用户: $(whoami)"
echo "Docker版本: $(docker --version)"
echo "Docker Compose版本: $(docker compose version)"
echo "可用磁盘空间: $(df -h /opt | tail -1 | awk '{print $4}')"
echo "内存信息: $(free -h | grep Mem | awk '{print $2 " 总计, " $7 " 可用"}')"

# Python环境检查
echo "=== Python环境检查 ==="
echo "Python版本: $(python3 --version)"
echo "pip版本: $(pip3 --version)"

# 检查项目目录
if [[ -d "/opt/matrix" ]]; then
    echo "✅ 项目目录存在: /opt/matrix"
else
    echo "❌ 项目目录不存在，请先创建项目目录结构"
fi

# 检查虚拟环境
if [[ -d "/opt/matrix/venv" ]]; then
    echo "✅ 虚拟环境存在: /opt/matrix/venv"

    # 检查虚拟环境中的RouterOS API库
    if /opt/matrix/venv/bin/python3 -c "import routeros_api" 2>/dev/null; then
        echo "✅ 虚拟环境中RouterOS API库可用"
        venv_path=$(/opt/matrix/venv/bin/python3 -c "import routeros_api; print(routeros_api.__file__)" 2>/dev/null)
        echo "   库路径: $venv_path"
    else
        echo "❌ 虚拟环境中RouterOS API库不可用"
        echo "   请激活虚拟环境并安装: source /opt/matrix/venv/bin/activate && pip install routeros-api==0.21.0"
    fi
else
    echo "⚠️  虚拟环境不存在，检查系统级安装"

    # 检查是否为Debian 12
    if [[ -f /etc/debian_version ]] && [[ $(cat /etc/debian_version) =~ ^12\. ]]; then
        echo "⚠️  检测到Debian 12，推荐使用虚拟环境"

        # 检查系统级RouterOS API库安装状态
        if python3 -c "import routeros_api" 2>/dev/null; then
            echo "✅ 系统级RouterOS API库已安装"
            python3 -c "import routeros_api; print('库路径:', routeros_api.__file__)"
        else
            echo "❌ RouterOS API库未安装"
            echo "   推荐方案: 创建虚拟环境并安装"
            echo "   备选方案: pip3 install --break-system-packages routeros-api==0.21.0"
        fi
    else
        echo "✅ 非Debian 12系统，检查标准安装"
        if python3 -c "import routeros_api" 2>/dev/null; then
            echo "✅ RouterOS API库已安装"
            python3 -c "import routeros_api; print('库路径:', routeros_api.__file__)"
        else
            echo "❌ RouterOS API库未安装，请运行: pip3 install routeros-api==0.21.0"
        fi
    fi
fi
```

#### 🔧 RouterOS设备验证

- [ ] **设备连通性**：Matrix服务器可以ping通RouterOS设备
- [ ] **API服务**：RouterOS API服务已启用并监听8728端口
- [ ] **用户权限**：matrix-api用户已创建并分配适当权限
- [ ] **访问控制**：API访问已限制到Matrix服务器IP
- [ ] **WAN接口**：能够正确识别和获取WAN接口IP地址

```bash
# RouterOS连接验证脚本
echo "=== RouterOS设备验证 ==="

# 配置参数（请根据实际情况修改）
ROUTEROS_HOST="***********"
ROUTEROS_PORT="8728"

# 1. 网络连通性测试
echo "1. 测试网络连通性..."
if ping -c 3 -W 3 "$ROUTEROS_HOST" >/dev/null 2>&1; then
    echo "✅ 网络连通性正常"
else
    echo "❌ 无法连接到RouterOS设备"
    exit 1
fi

# 2. API端口测试
echo "2. 测试API端口..."
if nc -zv "$ROUTEROS_HOST" "$ROUTEROS_PORT" 2>/dev/null; then
    echo "✅ API端口$ROUTEROS_PORT 可访问"
else
    echo "❌ API端口$ROUTEROS_PORT 不可访问"
    exit 1
fi

# 3. RouterOS API连接测试
echo "3. 测试RouterOS API连接..."
if [[ -f "/opt/matrix/tools/routeros_client.py" ]]; then
    cd /opt/matrix
    if python3 tools/routeros_client.py --host *********** --user matrix-api --password your_password test; then
        echo "✅ RouterOS API连接成功"
    else
        echo "❌ RouterOS API连接失败"
        exit 1
    fi
else
    echo "⚠️  项目工具未找到，请先下载项目代码"
    echo "   手动测试: telnet *********** 8728"
fi

echo "✅ RouterOS设备验证完成"
```

#### 🌐 网络和DNS验证

- [ ] **域名解析**：主域名和Matrix子域名DNS记录已正确配置
- [ ] **端口转发**：路由器已配置8448端口转发到Matrix服务器
- [ ] **防火墙**：服务器防火墙已配置允许必要端口
- [ ] **Cloudflare配置**：API令牌和区域ID已正确配置

```bash
# 网络和DNS验证脚本
echo "=== 网络和DNS验证 ==="

# 配置参数（请根据实际情况修改）
DOMAIN="example.com"
MATRIX_DOMAIN="matrix.example.com"
SERVER_IP="***********00"

# 1. DNS解析测试
echo "1. 测试DNS解析..."
main_ip=$(dig +short "$DOMAIN" | tail -1)
matrix_ip=$(dig +short "$MATRIX_DOMAIN" | tail -1)

echo "主域名 $DOMAIN 解析到: $main_ip"
echo "Matrix域名 $MATRIX_DOMAIN 解析到: $matrix_ip"

# 2. 端口连通性测试
echo "2. 测试端口连通性..."
if nc -zv "$matrix_ip" 8448 2>/dev/null; then
    echo "✅ 端口8448可访问"
else
    echo "⚠️  端口8448不可访问（部署后再次测试）"
fi

# 3. 防火墙状态检查
echo "3. 检查防火墙状态..."
if command -v ufw >/dev/null; then
    echo "UFW状态: $(sudo ufw status | head -1)"
    sudo ufw status numbered | grep -E "(8448|3478|5349)"
fi

echo "✅ 网络和DNS验证完成"
```

#### 🔐 SSL证书验证

- [ ] **acme.sh安装**：acme.sh已正确安装并配置
- [ ] **证书目录**：证书存储目录已创建并设置正确权限
- [ ] **符号链接**：证书符号链接路径已规划

```bash
# SSL证书环境验证
echo "=== SSL证书环境验证 ==="

# 1. 检查acme.sh安装
echo "1. 检查acme.sh安装..."
if command -v acme.sh >/dev/null; then
    echo "✅ acme.sh已安装: $(acme.sh --version)"
else
    echo "❌ acme.sh未安装"
    exit 1
fi

# 2. 检查证书目录
echo "2. 检查证书目录..."
if [ -d "/root/.acme.sh" ]; then
    echo "✅ acme.sh证书目录存在"
else
    echo "❌ acme.sh证书目录不存在"
fi

if [ -d "/opt/matrix/data/acme" ]; then
    echo "✅ Matrix证书目录存在"
else
    echo "⚠️  Matrix证书目录不存在（部署时会创建）"
fi

# 3. 检查目录权限
echo "3. 检查目录权限..."
ls -la /opt/matrix/data/ | grep acme

echo "✅ SSL证书环境验证完成"
```

### 综合配置验证

#### 使用项目验证工具

> 📝 **前提条件**：需要先下载Matrix Homeserver项目代码

```bash
# 运行项目内置的验证工具（如果项目代码已下载）
cd /opt/matrix

# 检查工具是否存在
if [[ -f "tools/validate_config.py" ]] && [[ -f "tools/routeros_client.py" ]]; then
    echo "=== 项目工具验证 ==="

    # 验证RouterOS配置
    python3 tools/validate_config.py --config-file internal/config/deployment.env

    # 测试RouterOS连接
    python3 tools/routeros_client.py --host *********** --user matrix-api --password your_password test

    # 获取WAN接口IP
    python3 tools/routeros_client.py --host *********** --user matrix-api --password your_password get-wan-ip

    # 列出网络接口
    python3 tools/routeros_client.py --host *********** --user matrix-api --password your_password list-interfaces

else
    echo "⚠️  项目工具未找到，使用手动验证方法"
    echo "请先下载Matrix Homeserver项目代码，或使用上述手动验证步骤"
fi
```

#### 预期验证结果

成功的综合验证应该显示：

```
=== Matrix Homeserver 配置验证报告 ===
生成时间: 2025-01-XX XX:XX:XX

✅ 系统环境检查通过
  - 操作系统: Debian GNU/Linux 12 (bookworm)
  - Docker版本: Docker version 24.0.x
  - 可用空间: 25G

✅ RouterOS设备检查通过
  - 网络连通性: 正常
  - API服务: 已启用
  - 用户认证: 成功
  - WAN接口: 检测成功

✅ 网络配置检查通过
  - DNS解析: 正常
  - 端口转发: 已配置
  - 防火墙: 已配置

✅ SSL证书环境检查通过
  - acme.sh: 已安装
  - 证书目录: 已准备

✅ 配置文件检查通过
  - deployment.env: 语法正确
  - 必需参数: 已配置
  - 权限设置: 正确

🎉 所有验证项目通过，可以开始部署！
```

### 验证失败处理

如果验证过程中发现问题，请按照以下步骤处理：

#### 🔴 关键错误（必须解决）

1. **RouterOS API连接失败**
   - 检查网络连通性
   - 验证API服务配置
   - 确认用户权限设置

2. **DNS解析失败**
   - 检查域名配置
   - 验证DNS记录
   - 确认Cloudflare设置

3. **系统环境不满足**
   - 升级操作系统版本
   - 安装缺失的软件包
   - 配置用户权限

#### 🟡 警告信息（建议解决）

1. **端口不可访问**
   - 部署完成后会自动解决
   - 确认路由器端口转发配置

2. **证书目录不存在**
   - 部署过程中会自动创建
   - 确认磁盘空间充足

#### 验证完成确认

完成所有验证后，请确认：

- [ ] **所有关键验证项目通过**
- [ ] **配置文件参数正确**
- [ ] **网络环境准备就绪**
- [ ] **RouterOS集成正常**
- [ ] **已生成验证报告**

> 🎯 **下一步**：验证通过后，可以继续进行Matrix Homeserver的正式部署。

---

## ✅ 部署验证

### 基础服务检查

```bash
# 1. 检查Docker服务状态
docker compose ps

# 期望输出：所有服务状态为"Up"
# NAME              IMAGE                     COMMAND                  SERVICE   CREATED         STATUS                   PORTS
# matrix_coturn     coturn/coturn:latest      "docker-entrypoint.s…"  coturn    2 minutes ago   Up 2 minutes (healthy)
# matrix_nginx      nginx:alpine              "/docker-entrypoint.…"  nginx     2 minutes ago   Up 2 minutes (healthy)   0.0.0.0:8448->8448/tcp
# matrix_postgres   postgres:15-alpine        "docker-entrypoint.s…"  db        2 minutes ago   Up 2 minutes (healthy)   5432/tcp
# matrix_redis      redis:7-alpine            "docker-entrypoint.s…"  redis     2 minutes ago   Up 2 minutes (healthy)   6379/tcp
# matrix_synapse    matrixdotorg/synapse:latest "python -m synapse.…"  synapse   2 minutes ago   Up 2 minutes (healthy)   8008/tcp

# 2. 检查服务健康状态
./scripts/health_check.sh

# 3. 检查端口监听
sudo netstat -tlnp | grep -E "(8448|3478|5349)"

# 期望输出：
# tcp        0      0 0.0.0.0:8448            0.0.0.0:*               LISTEN      12345/nginx
# tcp        0      0 0.0.0.0:3478            0.0.0.0:*               LISTEN      12346/turnserver
# tcp        0      0 0.0.0.0:5349            0.0.0.0:*               LISTEN      12346/turnserver
```

### Matrix API测试

```bash
# 1. 测试客户端API
curl -k https://matrix.example.com:8448/_matrix/client/versions

# 期望输出：JSON格式的版本信息
# {
#   "versions": [
#     "r0.0.1",
#     "r0.1.0",
#     "r0.2.0",
#     ...
#   ]
# }

# 2. 测试联邦API
curl -k https://matrix.example.com:8448/_matrix/federation/v1/version

# 期望输出：
# {
#   "server": {
#     "name": "Synapse",
#     "version": "1.x.x"
#   }
# }

# 3. 测试服务器信息
curl -k https://matrix.example.com:8448/_matrix/client/r0/login

# 期望输出：登录方法列表
```

### 外部指路牌服务测试

```bash
# 测试.well-known客户端发现
curl https://example.com/.well-known/matrix/client

# 期望输出：
# {
#   "m.homeserver": {
#     "base_url": "https://matrix.example.com:8448"
#   }
# }

# 测试.well-known服务器发现
curl https://example.com/.well-known/matrix/server

# 期望输出：
# {
#   "m.server": "matrix.example.com:8448"
# }
```

### SSL证书验证

```bash
# 1. 检查证书有效期
openssl x509 -in data/nginx/certs/fullchain.cer -noout -dates

# 2. 检查证书符号链接
ls -la data/nginx/certs/
ls -la data/coturn/certs/

# 3. 测试HTTPS连接
openssl s_client -connect matrix.example.com:8448 -servername matrix.example.com

# 4. 使用在线工具测试SSL配置
# https://www.ssllabs.com/ssltest/
```

### TURN服务器测试

```bash
# 1. 测试STUN服务
turnutils_stunclient matrix.example.com 3478

# 2. 测试TURN服务（需要有效凭证）
turnutils_uclient -t matrix.example.com -p 3478

# 3. 在线STUN/TURN测试工具
# https://webrtc.github.io/samples/src/content/peerconnection/trickle-ice/
```

### 客户端连接测试

1. **使用Element Web客户端**：
   - 访问：https://app.element.io
   - 选择"自定义服务器"
   - 输入服务器地址：`example.com`
   - 使用创建的管理员账户登录

2. **使用Element桌面客户端**：
   - 下载并安装Element桌面版
   - 配置自定义服务器：`example.com`
   - 登录测试

3. **使用其他Matrix客户端**：
   - FluffyChat（移动端）
   - Nheko（桌面端）
   - SchildiChat（Element分支）

---

## 🔧 故障排除指南

### Debian 12 Python环境故障排除

#### 🚨 Python包安装问题

##### 问题1：externally-managed-environment错误

**症状**：
```
error: externally-managed-environment

× This environment is externally managed
╰─> To install Python packages system-wide, try apt install
    python3-xyz, where xyz is the package you are trying to
    install.
```

**原因**：
Debian 12引入了PEP 668规范，防止用户直接在系统Python环境中安装包，避免与系统包管理器冲突。

**解决方案**：

**方案1：使用项目自动安装脚本（推荐）**
```bash
# 运行项目提供的自动安装脚本
cd /opt/matrix
./tools/install_routeros_deps.sh

# 脚本会自动检测Debian 12并处理环境问题
```

**方案2：使用--break-system-packages选项**
```bash
# 强制安装到系统环境（需要谨慎使用）
pip3 install --break-system-packages routeros-api==0.21.0

# 验证安装
python3 -c "import routeros_api; print('安装成功')"
```

**方案3：创建虚拟环境**
```bash
# 创建虚拟环境
python3 -m venv /opt/matrix/venv
source /opt/matrix/venv/bin/activate

# 在虚拟环境中安装
pip install routeros-api==0.21.0

# 修改脚本使用虚拟环境
# 将脚本的shebang改为：#!/opt/matrix/venv/bin/python3
```

##### 问题2：Python脚本无法执行

**症状**：
```
bash: ./script.py: Permission denied
# 或
python3: can't open file 'script.py': [Errno 2] No such file or directory
```

**解决方案**：

1. **检查文件权限**：
   ```bash
   # 查看文件权限
   ls -la script.py

   # 添加执行权限
   chmod +x script.py
   ```

2. **检查shebang行**：
   ```bash
   # 确保脚本第一行为：
   #!/usr/bin/env python3
   # 或
   #!/opt/matrix/venv/bin/python3  # 如果使用虚拟环境
   ```

3. **检查文件路径**：
   ```bash
   # 确认文件存在
   ls -la /opt/matrix/tools/routeros_client.py

   # 使用绝对路径运行
   python3 /opt/matrix/tools/routeros_client.py
   ```

##### 问题3：模块导入失败

**症状**：
```
ModuleNotFoundError: No module named 'routeros_api'
```

**排查步骤**：

1. **检查模块安装**：
   ```bash
   # 检查模块是否已安装
   python3 -c "import routeros_api"

   # 查看模块安装位置
   python3 -c "import routeros_api; print(routeros_api.__file__)"
   ```

2. **检查Python路径**：
   ```bash
   # 查看Python搜索路径
   python3 -c "import sys; print('\n'.join(sys.path))"

   # 查看已安装的包
   pip3 list | grep routeros
   ```

3. **重新安装模块**：
   ```bash
   # 卸载后重新安装
   pip3 uninstall routeros-api
   pip3 install --break-system-packages routeros-api==0.21.0
   ```

#### 🔧 Python环境诊断工具

```bash
# 创建Python环境诊断脚本
cat > /opt/matrix/tools/python_env_check.sh << 'EOF'
#!/bin/bash
"""Python环境诊断脚本"""

echo "=== Python环境诊断 ==="
echo "诊断时间: $(date)"
echo

# 1. 系统信息
echo "1. 系统信息"
echo "==========="
echo "操作系统: $(lsb_release -d | cut -f2)"
echo "内核版本: $(uname -r)"
if [[ -f /etc/debian_version ]]; then
    echo "Debian版本: $(cat /etc/debian_version)"
fi
echo

# 2. Python环境
echo "2. Python环境"
echo "============="
echo "Python版本: $(python3 --version)"
echo "Python路径: $(which python3)"
echo "pip版本: $(pip3 --version)"
echo "pip路径: $(which pip3)"
echo

# 3. 检查外部管理环境
echo "3. 外部管理环境检查"
echo "=================="
if [[ -f /usr/lib/python*/EXTERNALLY-MANAGED ]]; then
    echo "⚠️  检测到外部管理环境限制"
    echo "文件位置: $(ls /usr/lib/python*/EXTERNALLY-MANAGED)"
    echo "需要使用特殊安装方式"
else
    echo "✅ 未检测到外部管理环境限制"
fi
echo

# 4. RouterOS API库检查
echo "4. RouterOS API库检查"
echo "==================="
if python3 -c "import routeros_api" 2>/dev/null; then
    echo "✅ RouterOS API库已安装"
    python3 -c "
import routeros_api
print('库路径:', routeros_api.__file__)
print('可用模块:', [x for x in dir(routeros_api) if not x.startswith('_')])
"
else
    echo "❌ RouterOS API库未安装"
    echo "建议运行: ./tools/install_routeros_deps.sh"
fi
echo

# 5. 虚拟环境检查（重点检查）
echo "5. 虚拟环境检查"
echo "=============="
if [[ -d "/opt/matrix/venv" ]]; then
    echo "✅ 发现虚拟环境: /opt/matrix/venv"

    # 检查虚拟环境结构
    if [[ -f "/opt/matrix/venv/bin/python3" ]]; then
        venv_python_version=$(/opt/matrix/venv/bin/python3 --version)
        echo "虚拟环境Python版本: $venv_python_version"

        # 检查pip
        if [[ -f "/opt/matrix/venv/bin/pip" ]]; then
            venv_pip_version=$(/opt/matrix/venv/bin/pip --version | cut -d' ' -f2)
            echo "虚拟环境pip版本: $venv_pip_version"
        else
            echo "⚠️  虚拟环境中pip不可用"
        fi

        # 检查RouterOS API库
        if /opt/matrix/venv/bin/python3 -c "import routeros_api" 2>/dev/null; then
            echo "✅ 虚拟环境中RouterOS API可用"
            echo "   ✅ RouterOS API库功能正常"
            venv_api_path=$(/opt/matrix/venv/bin/python3 -c "import routeros_api; print(routeros_api.__file__)" 2>/dev/null)
            echo "   路径: $venv_api_path"
        else
            echo "❌ 虚拟环境中RouterOS API不可用"
            echo "   修复命令: source /opt/matrix/venv/bin/activate && pip install routeros-api==0.21.0"
        fi

        # 检查激活脚本
        if [[ -f "/opt/matrix/activate_venv.sh" ]]; then
            echo "✅ 虚拟环境激活脚本存在"
        else
            echo "⚠️  虚拟环境激活脚本不存在"
        fi
    else
        echo "❌ 虚拟环境Python解释器不存在"
    fi
else
    echo "❌ 未发现虚拟环境: /opt/matrix/venv"
    echo "   创建命令: cd /opt/matrix && python3 -m venv venv"
fi
echo

# 6. 建议和修复方案
echo "6. 建议和修复方案"
echo "================"
if [[ -f /etc/debian_version ]] && [[ $(cat /etc/debian_version) =~ ^12\. ]]; then
    echo "检测到Debian 12，推荐解决方案："
    echo ""
    echo "🎯 首选方案：虚拟环境（推荐）"
    echo "   cd /opt/matrix"
    echo "   python3 -m venv venv"
    echo "   source venv/bin/activate"
    echo "   pip install routeros-api==0.21.0"
    echo ""
    echo "🔧 备选方案1：项目自动安装脚本"
    echo "   ./tools/install_routeros_deps.sh"
    echo ""
    echo "⚠️  备选方案2：系统级强制安装"
    echo "   pip3 install --break-system-packages routeros-api==0.21.0"
    echo ""
    echo "💡 日常使用建议："
    echo "   - 使用别名: matrix-env (激活虚拟环境)"
    echo "   - 直接调用: /opt/matrix/venv/bin/python3 script.py"
    echo "   - 修改脚本shebang: #!/opt/matrix/venv/bin/python3"
else
    echo "非Debian 12系统，推荐方案："
    echo "1. 标准安装: pip3 install routeros-api==0.21.0"
    echo "2. 虚拟环境: 更好的隔离性和管理"
fi

echo
echo "=== 诊断完成 ==="
EOF

# 设置执行权限
chmod +x /opt/matrix/tools/python_env_check.sh

# 运行诊断
./tools/python_env_check.sh
```

### RouterOS API故障排除

#### 🔴 RouterOS连接问题

##### 问题1：连接被拒绝 (Connection refused)

**症状**：
```
❌ 连接失败: [Errno 111] Connection refused
```

**排查步骤**：

1. **检查网络连通性**：
   ```bash
   # 测试基础网络连接
   ping -c 3 ***********

   # 如果ping失败，检查：
   # - RouterOS设备是否开机
   # - 网络线缆是否连接正常
   # - IP地址是否正确
   ```

2. **检查API服务状态**：
   ```bash
   # 在RouterOS设备上执行
   /ip service print where name=api

   # 预期输出应显示API服务已启用
   # 如果显示 X (disabled)，执行：
   /ip service enable api
   ```

3. **检查API端口**：
   ```bash
   # 测试API端口连通性
   nc -zv *********** 8728
   telnet *********** 8728

   # 如果端口不通，在RouterOS上检查：
   /ip service print where name=api
   # 确认端口设置正确
   ```

4. **检查防火墙规则**：
   ```bash
   # 在RouterOS上检查防火墙规则
   /ip firewall filter print where dst-port=8728

   # 如果有阻止规则，添加允许规则：
   /ip firewall filter add chain=input protocol=tcp dst-port=8728 \
       src-address=***********00 action=accept place-before=0
   ```

##### 问题2：认证失败 (Authentication failed)

**症状**：
```
❌ 连接失败: Authentication failed
```

**排查步骤**：

1. **验证用户名和密码**：
   ```bash
   # 在RouterOS上检查用户是否存在
   /user print where name=matrix-api

   # 如果用户不存在，创建用户：
   /user add name=matrix-api group=read password=your_password
   ```

2. **检查用户状态**：
   ```bash
   # 确认用户未被禁用
   /user print where name=matrix-api
   # 如果显示 X (disabled)，启用用户：
   /user set matrix-api disabled=no
   ```

3. **重置用户密码**：
   ```bash
   # 重新设置密码
   /user set matrix-api password=new_secure_password
   ```

4. **检查用户权限**：
   ```bash
   # 确认用户组权限
   /user group print where name=read
   # 确保包含 api 和 read 权限
   ```

##### 问题3：权限不足 (Permission denied)

**症状**：
```
❌ 无法获取接口信息: Permission denied
```

**解决方案**：

1. **检查用户组权限**：
   ```bash
   # 查看当前用户组权限
   /user print where name=matrix-api
   /user group print where name=read

   # 如果权限不足，创建专用权限组：
   /user group add name=api-readonly policy=api,read
   /user set matrix-api group=api-readonly
   ```

2. **验证API权限**：
   ```bash
   # 测试API访问权限（如果项目工具可用）
   python3 tools/routeros_client.py --host *********** --user matrix-api --password your_password test

   # 或手动验证权限
   # 在RouterOS设备上检查用户权限
   /user print where name=matrix-api
   /user group print where name=read
   ```

#### 🟡 WAN接口检测问题

##### 问题1：找不到WAN接口

**症状**：
```
⚠️ 警告：未找到WAN接口 'WAN' 的IP地址
```

**解决方案**：

1. **查看所有接口**：
   ```bash
   # 在RouterOS上查看所有接口
   /interface print
   /ip address print

   # 记录实际的WAN接口名称
   ```

2. **更新配置文件**：
   ```bash
   # 编辑配置文件
   nano /opt/matrix/config/deployment.env

   # 修改WAN接口名称
   ROUTEROS_WAN_INTERFACE="ether1"  # 或实际的接口名称
   ```

3. **常见WAN接口名称**：
   - `WAN` - 默认WAN接口
   - `ether1` - 第一个以太网接口
   - `pppoe-out1` - PPPoE拨号接口
   - `lte1` - LTE移动网络接口

##### 问题2：获取到内网IP而非公网IP

**症状**：
```
⚠️ 获取到的IP是内网地址: ***********
```

**原因分析**：
- RouterOS设备本身处于NAT环境中
- WAN接口配置错误
- 运营商使用多层NAT

**解决方案**：

1. **检查网络拓扑**：
   ```bash
   # 在RouterOS上查看路由表
   /ip route print where dst-address=0.0.0.0/0

   # 查看默认网关
   /ip route print where gateway!=0.0.0.0
   ```

2. **配置备选IP获取方式**：
   ```bash
   # 编辑配置文件，启用外部IP查询
   nano /opt/matrix/config/deployment.env

   # 设置备选IP获取方式
   ENABLE_EXTERNAL_IP_FALLBACK="true"
   EXTERNAL_IP_SERVICES="ipify,icanhazip,ifconfig.me"
   ```

#### 🔵 IP监控服务问题

##### 问题1：IP监控脚本执行失败

**症状**：
```
❌ IP监控脚本执行失败
```

**排查步骤**：

1. **检查脚本权限**：
   ```bash
   # 确认脚本有执行权限
   ls -la /opt/matrix/internal/scripts/ip_watchdog.sh
   chmod +x /opt/matrix/internal/scripts/ip_watchdog.sh
   ```

2. **手动运行脚本**：
   ```bash
   # 手动执行脚本查看详细错误
   cd /opt/matrix
   ./internal/scripts/ip_watchdog.sh --debug
   ```

3. **检查依赖库**：
   ```bash
   # 确认RouterOS API库已安装
   python3 -c "import routeros_api; print('OK')"

   # 如果失败，重新安装：
   pip3 install routeros-api
   ```

##### 问题2：DNS更新失败

**症状**：
```
❌ DNS记录更新失败
```

**排查步骤**：

1. **验证Cloudflare配置**：
   ```bash
   # 测试Cloudflare API连接
   curl -X GET "https://api.cloudflare.com/client/v4/zones" \
        -H "Authorization: Bearer YOUR_API_TOKEN" \
        -H "Content-Type: application/json"
   ```

2. **检查API令牌权限**：
   - 确认令牌有Zone:DNS:Edit权限
   - 确认令牌有Zone:Zone:Read权限
   - 确认令牌适用于正确的域名

3. **手动测试DNS更新**：
   ```bash
   # 手动测试Cloudflare API连接
   curl -X GET "https://api.cloudflare.com/client/v4/zones" \
        -H "Authorization: Bearer YOUR_API_TOKEN" \
        -H "Content-Type: application/json"

   # 如果项目工具可用，可以使用：
   # python3 tools/dns_updater.py --test-update
   ```

#### 🟢 RouterOS故障排除工具

##### 自动诊断脚本

```bash
# 创建RouterOS故障排除脚本
cat > /opt/matrix/tools/routeros_troubleshoot.sh << 'EOF'
#!/bin/bash
"""RouterOS故障排除脚本"""

echo "=== RouterOS故障排除诊断 ==="
echo "开始时间: $(date)"
echo

# 配置参数
ROUTEROS_HOST="${ROUTEROS_HOST:-***********}"
ROUTEROS_PORT="${ROUTEROS_PORT:-8728}"

# 1. 网络连通性测试
echo "1. 网络连通性测试"
echo "=================="
if ping -c 3 -W 3 "$ROUTEROS_HOST" >/dev/null 2>&1; then
    echo "✅ 网络连通性正常"
    ping_time=$(ping -c 1 "$ROUTEROS_HOST" | grep 'time=' | awk -F'time=' '{print $2}' | awk '{print $1}')
    echo "   延迟: ${ping_time}ms"
else
    echo "❌ 网络连通性失败"
    echo "   建议检查："
    echo "   - RouterOS设备是否开机"
    echo "   - 网络线缆连接"
    echo "   - IP地址配置"
    exit 1
fi
echo

# 2. API端口测试
echo "2. API端口测试"
echo "=============="
if nc -zv "$ROUTEROS_HOST" "$ROUTEROS_PORT" 2>/dev/null; then
    echo "✅ API端口 $ROUTEROS_PORT 可访问"
else
    echo "❌ API端口 $ROUTEROS_PORT 不可访问"
    echo "   建议检查："
    echo "   - API服务是否启用"
    echo "   - 防火墙规则"
    echo "   - 端口配置"
fi
echo

# 3. RouterOS API连接测试
echo "3. RouterOS API连接测试"
echo "====================="
if [[ -f "/opt/matrix/tools/routeros_client.py" ]]; then
    cd /opt/matrix
    if python3 tools/routeros_client.py --host *********** --user matrix-api --password your_password test 2>/dev/null; then
        echo "✅ RouterOS API连接成功"
    else
        echo "❌ RouterOS API连接失败"
        echo "   请检查RouterOS配置和网络连接"
    fi
else
    echo "⚠️  项目工具未找到，手动测试："
    echo "   telnet *********** 8728"
fi
echo

# 4. 配置文件检查
echo "4. 配置文件检查"
echo "=============="
if [ -f "/opt/matrix/config/deployment.env" ]; then
    echo "✅ 配置文件存在"

    # 检查关键配置项
    if grep -q "ROUTEROS_HOST" /opt/matrix/config/deployment.env; then
        echo "✅ ROUTEROS_HOST 已配置"
    else
        echo "❌ ROUTEROS_HOST 未配置"
    fi

    if grep -q "ROUTEROS_USER" /opt/matrix/config/deployment.env; then
        echo "✅ ROUTEROS_USER 已配置"
    else
        echo "❌ ROUTEROS_USER 未配置"
    fi
else
    echo "❌ 配置文件不存在"
fi
echo

# 5. 依赖库检查
echo "5. 依赖库检查"
echo "============"
if python3 -c "import routeros_api" 2>/dev/null; then
    echo "✅ routeros_api 库已安装"
    lib_path=$(python3 -c "import routeros_api; print(routeros_api.__file__)" 2>/dev/null || echo "未知")
    echo "   库路径: $lib_path"
else
    echo "❌ routeros_api 库未安装"
    echo "   安装命令: pip3 install routeros-api"
fi
echo

echo "=== 诊断完成 ==="
echo "结束时间: $(date)"
EOF

# 设置执行权限
chmod +x /opt/matrix/tools/routeros_troubleshoot.sh

# 运行故障排除脚本
./tools/routeros_troubleshoot.sh
```

### 常见问题及解决方案

#### 1. 服务无法启动

**症状**：Docker容器启动失败或频繁重启

**排查步骤**：
```bash
# 查看容器状态
docker compose ps

# 查看详细日志
docker compose logs [service_name]

# 查看系统资源使用
htop
df -h

# 检查端口占用
sudo netstat -tlnp | grep -E "(8448|3478|5349|8008|5432|6379)"
```

**常见原因及解决方案**：

**端口被占用**：
```bash
# 查找占用端口的进程
sudo lsof -i :8448

# 停止占用端口的服务
sudo systemctl stop nginx  # 如果系统Nginx占用了端口
sudo kill -9 [PID]         # 强制终止进程
```

**权限问题**：
```bash
# 修复目录权限
sudo chown -R matrix:matrix /opt/matrix/data/
sudo chmod -R 755 /opt/matrix/data/
```

**内存不足**：
```bash
# 检查内存使用
free -h

# 调整Docker Compose资源限制
nano docker-compose.yml
# 减少各服务的memory限制
```

#### 2. SSL证书问题

**症状**：HTTPS连接失败，浏览器显示证书错误

**排查步骤**：
```bash
# 检查证书文件是否存在
ls -la data/nginx/certs/

# 检查证书有效期
openssl x509 -in data/nginx/certs/fullchain.cer -noout -dates

# 检查证书符号链接
./scripts/init_certificate_links.sh status

# 检查acme.sh证书状态
acme.sh --list
```

**解决方案**：
```bash
# 重新申请证书
./scripts/certificate_manager.sh --force-renew

# 重新初始化符号链接
./scripts/init_certificate_links.sh init

# 重启相关服务
docker compose restart nginx coturn
```

#### 3. 动态IP同步问题

**症状**：IP变更后服务不可达，DNS记录未更新

**排查步骤**：
```bash
# 检查当前公网IP
curl -s https://ipv4.icanhazip.com/

# 检查DNS解析
nslookup matrix.example.com

# 检查IP监控日志
tail -f /var/log/matrix/ip_watchdog.log

# 测试RouterOS连接
python3 /opt/matrix/test_routeros.py
```

**解决方案**：
```bash
# 手动触发IP更新
./scripts/ip_watchdog.sh --force-update

# 检查Cloudflare API配置
# 确保API Token和Zone ID正确

# 重新配置RouterOS连接
nano /opt/matrix/config/ip_watchdog.conf
```

#### 4. 数据库连接问题

**症状**：Synapse无法连接到PostgreSQL数据库

**排查步骤**：
```bash
# 检查数据库容器状态
docker compose ps db

# 检查数据库日志
docker compose logs db

# 测试数据库连接
docker compose exec db pg_isready -U synapse

# 手动连接数据库
docker compose exec db psql -U synapse -d synapse
```

**解决方案**：
```bash
# 重启数据库服务
docker compose restart db

# 检查数据库配置
nano config/homeserver.yaml
# 确保数据库密码正确

# 重新初始化数据库（谨慎操作）
docker compose down
docker volume rm matrix_postgres_data
docker compose up -d
```

#### 5. 联邦通信问题

**症状**：无法与其他Matrix服务器通信

**排查步骤**：
```bash
# 检查.well-known文件
curl https://example.com/.well-known/matrix/server

# 检查联邦API
curl -k https://matrix.example.com:8448/_matrix/federation/v1/version

# 检查防火墙设置
sudo ufw status

# 检查路由器端口转发
# 确保8448端口正确转发到内网服务器
```

**解决方案**：
```bash
# 确保外部指路牌服务正常运行
# 检查VPS上的Nginx配置

# 验证DNS配置
# 确保主域名A记录指向VPS IP
# 确保Matrix子域名A记录指向动态IP

# 检查Synapse联邦配置
nano config/homeserver.yaml
# 确保server_name和public_baseurl正确
```

### 日志分析

#### 重要日志文件位置

```bash
# Synapse日志
docker compose logs synapse
tail -f data/synapse/logs/homeserver.log

# Nginx日志
docker compose logs nginx
tail -f data/nginx/logs/access.log
tail -f data/nginx/logs/error.log

# PostgreSQL日志
docker compose logs db

# Redis日志
docker compose logs redis

# Coturn日志
tail -f data/coturn/logs/turnserver.log

# 系统脚本日志
tail -f /var/log/matrix/ip_watchdog.log
tail -f /var/log/matrix/certificate_manager.log
tail -f /var/log/matrix/health_check.log
```

#### 常见错误信息

**Synapse错误**：
```
# 数据库连接错误
psycopg2.OperationalError: could not connect to server

# 证书错误
SSL: CERTIFICATE_VERIFY_FAILED

# 内存不足
MemoryError: Unable to allocate memory
```

**Nginx错误**：
```
# 上游服务器连接失败
connect() failed (111: Connection refused) while connecting to upstream

# 证书文件不存在
SSL: error:02001002:system library:fopen:No such file or directory
```

### 性能优化

#### 系统级优化

```bash
# 调整系统文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 调整内核参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" >> /etc/sysctl.conf
sysctl -p

# 启用内存交换（如果内存不足）
fallocate -l 2G /swapfile
chmod 600 /swapfile
mkswap /swapfile
swapon /swapfile
echo '/swapfile none swap sw 0 0' >> /etc/fstab
```

#### 应用级优化

```bash
# 调整PostgreSQL配置
# 编辑 data/postgres/postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# 调整Redis配置
# 在docker-compose.yml中修改Redis命令
command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

# 调整Synapse配置
# 编辑 config/homeserver.yaml
database:
  args:
    cp_min: 10
    cp_max: 20
```

---

## 📅 日常维护

### 定期维护任务

#### 每日任务（自动化）

```bash
# 查看定时任务配置
crontab -l

# 定时任务说明：
# * * * * * /opt/matrix/scripts/ip_watchdog.sh          # 每分钟检查IP变化
# 0 2 * * * /opt/matrix/scripts/certificate_manager.sh  # 每天2点检查证书
# */5 * * * * /opt/matrix/scripts/health_check.sh       # 每5分钟健康检查
# 0 3 * * * /opt/matrix/scripts/backup.sh               # 每天3点备份数据
```

#### 每周任务（手动）

```bash
# 1. 检查系统更新
sudo apt update && sudo apt list --upgradable

# 2. 检查Docker镜像更新
docker compose pull

# 3. 清理Docker资源
docker system prune -f

# 4. 检查磁盘空间
df -h
du -sh /opt/matrix/data/*

# 5. 检查日志文件大小
du -sh /var/log/matrix/*
```

#### 每月任务（手动）

```bash
# 1. 更新系统软件包
sudo apt upgrade -y

# 2. 更新Docker镜像并重启服务
docker compose pull
docker compose up -d

# 3. 清理旧的备份文件
find /opt/matrix/data/backup/ -name "*.tar.gz" -mtime +30 -delete

# 4. 检查证书有效期
./scripts/certificate_manager.sh --check-expiry

# 5. 数据库维护
./scripts/admin.sh db vacuum
```

### 监控和告警

#### 设置邮件告警

```bash
# 安装邮件工具
sudo apt install mailutils -y

# 配置邮件发送
sudo nano /etc/postfix/main.cf

# 创建告警脚本
cat > /opt/matrix/scripts/alert.sh << 'EOF'
#!/bin/bash
# 系统告警脚本

ADMIN_EMAIL="<EMAIL>"
HOSTNAME=$(hostname)

send_alert() {
    local subject="$1"
    local message="$2"

    echo "$message" | mail -s "[$HOSTNAME] $subject" "$ADMIN_EMAIL"
}

# 检查服务状态
if ! ./scripts/health_check.sh >/dev/null 2>&1; then
    send_alert "Matrix服务异常" "Matrix Homeserver健康检查失败，请及时处理。"
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/matrix | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    send_alert "磁盘空间不足" "磁盘使用率已达到 ${DISK_USAGE}%，请及时清理。"
fi

# 检查内存使用
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
if [ "$MEMORY_USAGE" -gt 90 ]; then
    send_alert "内存使用率过高" "内存使用率已达到 ${MEMORY_USAGE}%，请检查系统状态。"
fi
EOF

chmod +x /opt/matrix/scripts/alert.sh

# 添加到定时任务
echo "*/10 * * * * /opt/matrix/scripts/alert.sh" | crontab -
```

### 备份和恢复

#### 自动备份配置

备份脚本会自动备份以下内容：
- PostgreSQL数据库
- Synapse配置和媒体文件
- SSL证书
- 配置文件

```bash
# 查看备份脚本配置
cat scripts/backup.sh

# 手动执行备份
./scripts/backup.sh

# 查看备份文件
ls -la data/backup/
```

#### 恢复流程

```bash
# 1. 停止所有服务
docker compose down

# 2. 恢复数据库
gunzip -c data/backup/db_backup_YYYYMMDD_HHMMSS.sql.gz | \
docker compose exec -T db psql -U synapse -d synapse

# 3. 恢复配置文件
tar -xzf data/backup/config_backup_YYYYMMDD_HHMMSS.tar.gz -C /

# 4. 恢复媒体文件
tar -xzf data/backup/media_backup_YYYYMMDD_HHMMSS.tar.gz -C data/synapse/

# 5. 重启服务
docker compose up -d

# 6. 验证恢复
./scripts/health_check.sh
```

### 安全维护

#### 定期安全检查

```bash
# 1. 检查系统安全更新
sudo apt list --upgradable | grep -i security

# 2. 检查登录日志
sudo tail -f /var/log/auth.log

# 3. 检查防火墙状态
sudo ufw status verbose

# 4. 检查SSL证书安全性
# 使用在线工具：https://www.ssllabs.com/ssltest/

# 5. 检查Docker容器安全
docker scan matrixdotorg/synapse:latest
```

#### 更新密钥和密码

```bash
# 1. 更新数据库密码
# 生成新密码
NEW_DB_PASSWORD=$(openssl rand -base64 32)

# 更新配置文件
sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$NEW_DB_PASSWORD/" config/deployment.env

# 更新数据库用户密码
docker compose exec db psql -U postgres -c "ALTER USER synapse PASSWORD '$NEW_DB_PASSWORD';"

# 重启Synapse服务
docker compose restart synapse

# 2. 更新Coturn共享密钥
NEW_COTURN_SECRET=$(openssl rand -base64 32)
sed -i "s/COTURN_SHARED_SECRET=.*/COTURN_SHARED_SECRET=$NEW_COTURN_SECRET/" config/deployment.env

# 重新生成配置文件
./scripts/setup.sh --skip-deps --skip-certs

# 重启相关服务
docker compose restart synapse coturn
```

---

## 📚 附录

### 有用的命令参考

```bash
# Docker Compose常用命令
docker compose ps                    # 查看服务状态
docker compose logs -f [service]     # 查看服务日志
docker compose restart [service]     # 重启服务
docker compose pull                  # 拉取最新镜像
docker compose up -d                 # 启动所有服务
docker compose down                  # 停止所有服务

# Matrix管理命令
./scripts/admin.sh user list         # 列出用户
./scripts/admin.sh user create       # 创建用户
./scripts/admin.sh room list         # 列出房间
./scripts/admin.sh db backup         # 备份数据库

# 系统监控命令
htop                                 # 系统资源监控
iotop                                # 磁盘IO监控
netstat -tlnp                        # 网络端口监控
df -h                                # 磁盘空间检查
free -h                              # 内存使用检查
```

### 配置文件模板

所有配置文件模板都位于 `config/` 目录下：
- `deployment.env.template` - 环境变量配置模板
- `homeserver.yaml.template` - Synapse配置模板
- `nginx.conf.template` - Nginx配置模板
- `matrix.conf.template` - Matrix站点配置模板
- `turnserver.conf.template` - Coturn配置模板

### 相关链接

- [Matrix官方文档](https://matrix.org/docs/)
- [Synapse管理员指南](https://matrix-org.github.io/synapse/latest/)
- [Element客户端](https://element.io/)
- [acme.sh文档](https://github.com/acmesh-official/acme.sh)
- [Coturn文档](https://github.com/coturn/coturn)
- [Docker Compose文档](https://docs.docker.com/compose/)

---

## 🔒 安全配置最佳实践

### RouterOS安全配置

#### 🛡️ API访问安全

**1. 用户权限最小化**
```bash
# 创建专用只读权限组
/user group add name=matrix-readonly policy=api,read

# 创建专用API用户
/user add name=matrix-api group=matrix-readonly password=strong_password_here

# 避免使用admin账户进行API访问
```

**2. 网络访问控制**
```bash
# 限制API访问来源IP
/ip service set api address=***********00/32

# 或限制到内网段
/ip service set api address=***********/24
```

**3. 防火墙规则配置**
```bash
# 添加API端口访问控制
/ip firewall filter add chain=input protocol=tcp dst-port=8728 \
    src-address=***********00 action=accept comment="Matrix API Access"

# 阻止其他来源访问API
/ip firewall filter add chain=input protocol=tcp dst-port=8728 \
    action=drop comment="Block unauthorized API access"
```

#### 🔐 密码安全策略

> 🚨 **安全警告**：弱密码是网络安全的最大威胁之一！必须为所有用户账户设置强密码。

**强密码要求**：
- **最少16个字符**（RouterOS API用户推荐20个字符以上）
- **包含大小写字母**：A-Z, a-z
- **包含数字**：0-9
- **包含特殊字符**：!@#$%^&*()_+-=[]{}|;:,.<>?
- **避免使用**：字典词汇、个人信息、简单模式
- **定期更换**：建议3-6个月更换一次

**密码生成最佳实践**：
```bash
# 方法1：使用openssl生成随机密码（推荐）
openssl rand -base64 32
# 输出示例：K8mN2pQ7vX9wR5tY3uI6oE1sA4dF2hJ9

# 方法2：使用pwgen工具
sudo apt install pwgen
pwgen -s 24 1
# 输出示例：xK9mP2qR7vN5wT8yU3iO6eA1

# 方法3：组合方式创建
# 基础词 + 年份 + 特殊字符 + 随机数
# 示例：Matrix2025!@API#7829
```

**密码管理和更换**：
```bash
# 更换matrix用户密码
sudo passwd matrix

# 更换RouterOS API用户密码
/user set matrix-api password=new_strong_password

# 验证密码更改
/user print where name=matrix-api
```

**密码存储安全**：
- ✅ **使用密码管理器**：如1Password、Bitwarden、KeePass
- ✅ **加密存储**：配置文件中的密码应加密存储
- ✅ **限制访问**：配置文件权限设置为600
- ❌ **避免明文**：不要在脚本或日志中明文记录密码
- ❌ **避免重复**：不要在多个系统中使用相同密码

**密码安全检查清单**：
- [ ] matrix用户密码已设置（12位以上强密码）
- [ ] RouterOS admin密码已更改（不使用默认密码）
- [ ] RouterOS API用户密码已设置（16位以上强密码）
- [ ] 所有密码都不相同
- [ ] 密码已安全记录在密码管理器中
- [ ] 配置文件权限已正确设置（chmod 600）

#### 📊 安全审计

**1. 定期检查API访问日志**
```bash
# 查看系统日志
/log print where topics~"api"

# 查看用户登录记录
/log print where message~"logged in"
```

**2. 监控异常连接**
```bash
# 检查活动连接
/ip firewall connection print where dst-port=8728

# 监控失败的认证尝试
/log print where message~"authentication failed"
```

### Matrix服务器安全

#### 🔒 系统安全

**1. 操作系统安全**
```bash
# 定期更新系统
sudo apt update && sudo apt upgrade -y

# 配置自动安全更新
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

**2. SSH安全配置**
```bash
# 编辑SSH配置
sudo nano /etc/ssh/sshd_config

# 推荐配置：
# Port 22                          # 或更改为非标准端口
# PermitRootLogin no               # 禁止root直接登录
# PasswordAuthentication no        # 禁用密码认证（使用密钥）
# PubkeyAuthentication yes         # 启用公钥认证
# MaxAuthTries 3                   # 限制认证尝试次数

# 重启SSH服务
sudo systemctl restart sshd
```

**3. 防火墙配置**
```bash
# 配置UFW防火墙
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 8448/tcp
sudo ufw allow 3478/tcp
sudo ufw allow 5349/tcp
sudo ufw enable
```

#### 🔐 应用安全

**1. Docker安全**
```bash
# 定期更新Docker镜像
docker compose pull
docker compose up -d

# 清理未使用的镜像
docker image prune -f
```

**2. 证书安全**
```bash
# 设置证书文件权限
chmod 600 /root/.acme.sh/*/private.key
chmod 644 /root/.acme.sh/*/fullchain.cer

# 定期检查证书有效期
acme.sh --list
```

### 安全监控

#### 📈 日志监控

**1. 系统日志监控**
```bash
# 监控认证失败
sudo tail -f /var/log/auth.log | grep "authentication failure"

# 监控SSH连接
sudo tail -f /var/log/auth.log | grep "sshd"
```

**2. Matrix服务日志监控**
```bash
# 监控Synapse日志
docker compose logs -f synapse | grep -E "(ERROR|WARN)"

# 监控Nginx访问日志
docker compose logs -f nginx | grep -E "(40[0-9]|50[0-9])"
```

#### 🚨 安全告警

**配置邮件告警**（可选）：
```bash
# 安装邮件工具
sudo apt install mailutils

# 配置告警脚本
cat > /opt/matrix/scripts/security_alert.sh << 'EOF'
#!/bin/bash
# 安全告警脚本

ALERT_EMAIL="<EMAIL>"
LOG_FILE="/var/log/matrix/security.log"

# 检查失败的SSH登录
failed_ssh=$(sudo grep "authentication failure" /var/log/auth.log | tail -10)
if [ -n "$failed_ssh" ]; then
    echo "SSH认证失败告警: $failed_ssh" | mail -s "Matrix服务器安全告警" $ALERT_EMAIL
fi

# 检查RouterOS API异常
api_errors=$(grep "RouterOS API" /var/log/matrix/ip_watchdog.log | grep "ERROR" | tail -5)
if [ -n "$api_errors" ]; then
    echo "RouterOS API异常: $api_errors" | mail -s "RouterOS API告警" $ALERT_EMAIL
fi
EOF

chmod +x /opt/matrix/scripts/security_alert.sh

# 添加到定时任务
echo "0 */6 * * * /opt/matrix/scripts/security_alert.sh" | crontab -
```

---

## 📋 配置检查清单

### 部署前检查清单

#### 🖥️ 系统环境
- [ ] **操作系统**：Debian 12 或 Ubuntu 22.04 LTS
- [ ] **用户配置**：matrix用户已创建，sudo权限已配置
- [ ] **Docker环境**：Docker 和 Docker Compose 已安装
- [ ] **磁盘空间**：至少20GB可用空间
- [ ] **内存配置**：至少4GB RAM（推荐8GB）
- [ ] **网络连接**：服务器可正常访问互联网
- [ ] **Python环境**：Python 3.9+已安装，pip3可用
- [ ] **项目目录**：/opt/matrix目录已创建，权限正确
- [ ] **虚拟环境**：Python虚拟环境已创建并配置
- [ ] **RouterOS API库**：在虚拟环境中已安装routeros-api==0.21.0
- [ ] **环境脚本**：虚拟环境激活脚本和别名已配置

#### 🔧 RouterOS设备
- [ ] **设备型号**：支持RouterOS 6.43+的MikroTik设备
- [ ] **网络连通**：Matrix服务器可ping通RouterOS设备
- [ ] **API服务**：RouterOS API服务已启用
- [ ] **用户配置**：matrix-api用户已创建并分配权限
- [ ] **密码安全**：API用户已设置强密码（16位以上）
- [ ] **访问控制**：API访问已限制到Matrix服务器IP
- [ ] **WAN接口**：WAN接口名称已确认
- [ ] **防火墙**：防火墙规则已正确配置

#### 🌐 网络和DNS
- [ ] **域名准备**：主域名和Matrix子域名已准备
- [ ] **DNS配置**：DNS记录已正确设置
- [ ] **Cloudflare**：API令牌和区域ID已获取
- [ ] **端口转发**：路由器8448端口转发已配置
- [ ] **防火墙**：服务器防火墙已配置

#### 🔐 SSL证书
- [ ] **acme.sh安装**：acme.sh已正确安装
- [ ] **证书目录**：证书存储目录已创建
- [ ] **权限设置**：目录权限已正确设置

### 部署后验证清单

#### ✅ 服务状态
- [ ] **Docker容器**：所有容器状态为"Up"且健康
- [ ] **端口监听**：8448、3478、5349端口正常监听
- [ ] **服务健康**：健康检查脚本运行正常

#### ✅ 功能测试
- [ ] **Matrix API**：客户端API响应正常
- [ ] **联邦API**：联邦API响应正常
- [ ] **Well-known**：.well-known文件可正常访问
- [ ] **SSL证书**：HTTPS连接正常，证书有效

#### ✅ RouterOS集成
- [ ] **API连接**：RouterOS API连接测试通过
- [ ] **IP获取**：能够正确获取WAN接口IP
- [ ] **DNS更新**：IP变化时DNS记录自动更新
- [ ] **监控服务**：IP监控定时任务正常运行

#### ✅ 安全配置
- [ ] **用户权限**：RouterOS API用户权限最小化
- [ ] **访问控制**：API访问来源已限制
- [ ] **防火墙**：防火墙规则已正确配置
- [ ] **证书权限**：证书文件权限已正确设置

### 日常维护检查清单

#### 🔄 定期检查（每周）
- [ ] **服务状态**：检查所有Docker容器状态
- [ ] **磁盘空间**：检查磁盘使用情况
- [ ] **日志文件**：检查错误日志
- [ ] **证书有效期**：检查SSL证书剩余有效期

#### 🔄 定期检查（每月）
- [ ] **系统更新**：更新操作系统和软件包
- [ ] **Docker镜像**：更新Docker镜像到最新版本
- [ ] **备份验证**：验证备份文件完整性
- [ ] **安全审计**：检查安全日志和异常访问

#### 🔄 定期检查（每季度）
- [ ] **密码更换**：更换RouterOS API用户密码
- [ ] **配置审查**：审查所有配置文件
- [ ] **性能优化**：检查系统性能并优化
- [ ] **文档更新**：更新部署文档和配置记录

### 故障排除检查清单

#### 🔴 服务无法启动
- [ ] 检查Docker服务状态
- [ ] 检查端口占用情况
- [ ] 检查目录权限
- [ ] 检查配置文件语法
- [ ] 检查系统资源使用

#### 🔴 RouterOS连接失败
- [ ] 检查网络连通性
- [ ] 检查API服务状态
- [ ] 检查用户认证信息
- [ ] 检查防火墙规则
- [ ] 检查API端口配置

#### 🔴 SSL证书问题
- [ ] 检查证书文件存在性
- [ ] 检查证书有效期
- [ ] 检查域名解析
- [ ] 检查acme.sh配置
- [ ] 检查符号链接

#### 🔴 DNS更新失败
- [ ] 检查Cloudflare API配置
- [ ] 检查API令牌权限
- [ ] 检查网络连接
- [ ] 检查DNS记录配置
- [ ] 检查IP监控脚本

---

## 📚 常见问题FAQ

### Debian 12 Python环境问题

**Q1: 为什么在Debian 12上无法直接使用pip3 install？**

A: Debian 12引入了PEP 668规范，实施"外部管理环境"策略，防止用户安装的Python包与系统包管理器冲突。这是一个安全特性，不是bug。

**Q2: 什么是"externally-managed-environment"错误？**

A: 这是Debian 12的新安全机制。当你尝试在系统Python环境中安装包时会出现此错误。解决方案：
- 使用`--break-system-packages`选项
- 创建虚拟环境
- 使用系统包管理器安装

**Q3: 使用--break-system-packages安全吗？**

A: 对于Matrix Homeserver项目是安全的，因为：
- 项目使用的是稳定的routeros-api库
- 不会与系统关键包冲突
- 项目有完整的依赖管理

但在生产环境中，推荐使用虚拟环境。

**Q4: 如何检查我的系统是否为Debian 12？**

A: 运行以下命令：
```bash
cat /etc/debian_version
# 如果输出12.x，则为Debian 12

lsb_release -a
# 查看详细系统信息
```

**Q5: 虚拟环境和系统级安装有什么区别？**

A:
- **虚拟环境**：隔离的Python环境，不影响系统，但需要激活后使用
- **系统级安装**：全局可用，但可能与系统包冲突

对于Matrix项目，两种方式都可以，系统级安装更简单。

**Q6: 如何在虚拟环境中运行Matrix脚本？**

A: 三种方式：
```bash
# 方式1：使用便捷激活脚本（推荐）
source /opt/matrix/activate_venv.sh
python3 script.py

# 方式2：手动激活虚拟环境
source /opt/matrix/venv/bin/activate
python3 script.py

# 方式3：直接使用虚拟环境的Python解释器
/opt/matrix/venv/bin/python3 script.py

# 方式4：使用配置的别名
matrix-env  # 激活虚拟环境
python3 script.py
```

**Q7: 为什么推荐使用虚拟环境而不是系统级安装？**

A: 虚拟环境的优势：
- **隔离性**：不影响系统Python环境
- **安全性**：避免与系统包冲突
- **可维护性**：可以轻松重建和管理
- **兼容性**：完美解决Debian 12的限制
- **可移植性**：环境配置可以复制到其他服务器

**Q8: 如何重建虚拟环境？**

A: 如果虚拟环境出现问题，可以重建：
```bash
# 删除现有虚拟环境
rm -rf /opt/matrix/venv

# 重新创建
cd /opt/matrix
python3 -m venv venv

# 激活并安装依赖
source venv/bin/activate
pip install --upgrade pip
pip install routeros-api==0.21.0

# 验证安装
python3 -c "import routeros_api; print('重建成功')"
```

**Q9: 虚拟环境会影响性能吗？**

A: 不会。虚拟环境只是改变了Python包的搜索路径，对运行时性能没有影响。启动时间的差异可以忽略不计。

**Q10: 如何在脚本中自动激活虚拟环境？**

A: 两种方法：
```bash
# 方法1：修改脚本的shebang行
#!/opt/matrix/venv/bin/python3

# 方法2：在脚本开头激活虚拟环境
#!/bin/bash
source /opt/matrix/venv/bin/activate
python3 actual_script.py
```

**Q11: 项目的自动安装脚本做了什么？**

A: `./tools/install_routeros_deps.sh`脚本会：
- 自动检测系统版本（Debian/Ubuntu）
- 检查Python和pip环境
- 选择合适的安装方式（Debian 12使用--break-system-packages）
- 安装routeros-api库和其他依赖
- 设置脚本权限
- 创建测试脚本
- 验证安装结果

> 📝 **注意**：此脚本需要在下载项目代码后才能使用。

### RouterOS相关问题

**Q1: 我的RouterOS版本是6.40，可以使用吗？**

A: 不建议使用。项目要求RouterOS 6.43或更高版本，因为需要`plaintext_login=True`参数支持。建议升级到RouterOS 7.x最新稳定版。

**Q2: 如何查看RouterOS版本？**

A: 在RouterOS中执行：`/system resource print`，查看version字段。

**Q3: API用户需要什么权限？**

A: 最小权限为`api,read`。可以创建专用权限组：
```bash
/user group add name=matrix-readonly policy=api,read
/user add name=matrix-api group=matrix-readonly password=strong_password
```

**Q4: 如何更改WAN接口名称？**

A: 编辑配置文件`/opt/matrix/config/deployment.env`，修改`ROUTEROS_WAN_INTERFACE`参数为实际的接口名称。

**Q5: RouterOS API连接超时怎么办？**

A: 检查网络连通性、API服务状态、防火墙规则。可以增加超时时间：`ROUTEROS_TIMEOUT="30"`。

### 网络和DNS问题

**Q6: 如何获取Cloudflare API令牌？**

A: 登录Cloudflare控制台 → My Profile → API Tokens → Create Token → 选择Custom token → 设置Zone:DNS:Edit和Zone:Zone:Read权限。

**Q7: DNS记录多久更新一次？**

A: 默认每分钟检查一次IP变化。可以通过`IP_CHECK_INTERVAL`参数调整检查间隔。

**Q8: 支持其他DNS服务商吗？**

A: 目前主要支持Cloudflare。其他DNS服务商需要相应的API集成开发。

### 部署和配置问题

**Q9: 部署需要多长时间？**

A: 对于有经验的用户，约1-2小时。技术初学者可能需要3-4小时，包括学习和故障排除时间。

**Q10: 可以在虚拟机中部署吗？**

A: 可以，但需要确保虚拟机有足够的资源（至少4GB RAM，20GB磁盘空间）和网络连接。

**Q11: 如何备份配置？**

A: 运行备份脚本：`/opt/matrix/scripts/backup.sh`，会自动备份配置文件、数据库和媒体文件。

**Q12: 如何迁移到新服务器？**

A: 备份原服务器数据，在新服务器上部署项目，然后恢复备份数据。详细步骤参考日常维护章节。

---

## 🎉 结语

恭喜你！如果你按照本指南完成了所有步骤，你现在拥有了一个功能完整的Matrix Homeserver。这个部署方案具有以下特点：

✅ **高可用性**：分离式架构确保服务稳定性
✅ **自动化运维**：智能证书管理和IP监控
✅ **安全可靠**：完整的SSL加密和防火墙配置
✅ **易于维护**：详细的监控和备份机制
✅ **技术友好**：详细的中文文档和故障排除指南

### 🚀 下一步建议

1. **熟悉管理界面**：学习使用Element客户端管理你的Matrix服务器
2. **邀请用户**：开始邀请朋友和同事使用你的Matrix服务器
3. **监控维护**：定期检查服务状态和日志文件
4. **安全加固**：根据安全最佳实践定期审查配置
5. **功能扩展**：探索Matrix的高级功能和集成选项

### 📞 获取帮助

如果在部署过程中遇到问题：

1. **查阅文档**：首先参考本指南的故障排除章节
2. **检查日志**：查看详细的错误日志信息
3. **社区支持**：访问Matrix社区获取帮助
4. **项目仓库**：查看GitHub仓库的Issues和Wiki

### 🙏 致谢

感谢你选择Matrix Homeserver项目！你的使用和反馈将帮助我们不断改进这个项目。

祝你使用愉快！🚀
